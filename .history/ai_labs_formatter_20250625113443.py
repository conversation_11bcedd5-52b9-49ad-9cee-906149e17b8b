#!/usr/bin/env python3
"""
AI-Powered Lab Results Formatter
Advanced medical lab results processing with AI integration
"""

import streamlit as st
import pandas as pd
import re
import json
import os
from datetime import datetime
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from anthropic import Anthropic
# OpenAI import is handled dynamically in the AILabFormatter class
import PyPDF2
import pytesseract
from PIL import Image
import base64
import nltk
import openai
import spacy
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import seaborn as sns
import matplotlib.pyplot as plt
from io import BytesIO

# Download required NLTK data
try:
    nltk.download('punkt', quiet=True)
    nltk.download('stopwords', quiet=True)
    nltk.download('wordnet', quiet=True)
except:
    pass

# Load spaCy model for Portuguese
try:
    nlp = spacy.load("pt_core_news_sm")
except:
    try:
        os.system("python -m spacy download pt_core_news_sm")
        nlp = spacy.load("pt_core_news_sm")
    except:
        # Fallback to English model
        nlp = spacy.load("en_core_web_sm")

@dataclass
class LabResult:
    """Data class for lab results"""
    test_name: str
    value: str
    unit: str
    reference_range: str
    status: str = "normal"  # normal, high, low, critical
    category: str = ""

@dataclass
class ProcessedResults:
    """Data class for processed lab results"""
    formatted_text: str
    alerts: List[Dict]
    raw_data: List[LabResult]
    summary: str
    recommendations: str

class ReferenceRanges:
    """Medical reference ranges for common lab tests - Ultra Complete Database"""

    # Ultra compact abbreviations dictionary for standardized formatting
    # === ULTRA EXPANDED DICTIONARY - DICIONÁRIO ULTRA EXPANDIDO ===
    # Mapeamento completo de exames laboratoriais com variações de nomenclatura
    EXAMES_DICT = {
        # === HEMOGRAMA COMPLETO ULTRA EXPANDIDO ===
        "Hemoglobina": "Hb", "hemoglobina": "Hb", "HEMOGLOBINA": "Hb", "Hb": "Hb",
        "Hematócrito": "Ht", "hematocrito": "Ht", "HEMATÓCRITO": "Ht", "Ht": "Ht",
        "Eritrócitos": "Hem", "eritrocitos": "Hem", "ERITRÓCITOS": "Hem", "Hemácias": "Hem", "hemacias": "Hem",
        "Leucócitos": "Leuco", "leucocitos": "Leuco", "LEUCÓCITOS": "Leuco", "Glóbulos Brancos": "Leuco",
        "Neutrófilos": "N", "neutrofilos": "N", "NEUTRÓFILOS": "N", "Neutrófilos Segmentados": "N",
        "Linfócitos": "L", "linfocitos": "L", "LINFÓCITOS": "L",
        "Monócitos": "Mono", "monocitos": "Mono", "MONÓCITOS": "Mono",
        "Eosinófilos": "Eos", "eosinofilos": "Eos", "EOSINÓFILOS": "Eos",
        "Basófilos": "Bas", "basofilos": "Bas", "BASÓFILOS": "Bas",
        "Plaquetas": "Plaq", "plaquetas": "Plaq", "PLAQUETAS": "Plaq",
        "Volume Corpuscular Médio": "VCM", "VCM": "VCM", "V.C.M.": "VCM", "vcm": "VCM",
        "Concentração de Hemoglobina Corpuscular Média": "CHCM", "CHCM": "CHCM", "C.H.C.M.": "CHCM", "chcm": "CHCM",
        "Amplitude de Distribuição Eritrocitária": "RDW", "RDW": "RDW", "R.D.W.": "RDW", "rdw": "RDW",
        "Hemoglobina Corpuscular Média": "HCM", "HCM": "HCM", "H.C.M.": "HCM", "hcm": "HCM",
        "Reticulócitos": "Retic", "reticulocitos": "Retic", "RETICULÓCITOS": "Retic",

        # === BIOQUÍMICA SÉRICA ULTRA EXPANDIDA ===
        "Glicose": "Gli", "glicose": "Gli", "GLICOSE": "Gli", "Glucose": "Gli",
        "Glicose de Jejum": "GliJ", "glicose jejum": "GliJ", "GLICOSE JEJUM": "GliJ", "Glicemia de Jejum": "GliJ",
        "Glicose Pós-Prandial": "GliPP", "glicose pos prandial": "GliPP", "Glicemia Pós-Prandial": "GliPP",
        "Hemoglobina Glicada": "HbA1c", "hemoglobina glicada": "HbA1c", "A1C": "HbA1c", "HbA1c": "HbA1c",
        "Frutosamina": "Frut", "frutosamina": "Frut", "FRUTOSAMINA": "Frut",

        # === FUNÇÃO RENAL ULTRA EXPANDIDA ===
        "Creatinina": "Cr", "creatinina": "Cr", "CREATININA": "Cr", "Cr": "Cr",
        "Uréia": "Ureia", "ureia": "Ureia", "URÉIA": "Ureia", "UREIA": "Ureia", "Urea": "Ureia",
        "Ácido Úrico": "AU", "acido urico": "AU", "ÁCIDO ÚRICO": "AU", "Urato": "AU",
        "Clearance de Creatinina": "ClCr", "clearance creatinina": "ClCr", "Depuração de Creatinina": "ClCr",
        "Cistatina C": "CisC", "cistatina c": "CisC", "CISTATINA C": "CisC",
        "Microalbuminúria": "MAlb", "microalbuminuria": "MAlb", "MICROALBUMINÚRIA": "MAlb",

        # === ELETRÓLITOS ULTRA EXPANDIDOS ===
        "Sódio": "Na", "sodio": "Na", "SÓDIO": "Na", "Na": "Na", "Sodium": "Na",
        "Potássio": "K", "potassio": "K", "POTÁSSIO": "K", "K": "K", "Potassium": "K",
        "Cloro": "Cl", "cloro": "Cl", "CLORO": "Cl", "Cl": "Cl", "Chloride": "Cl",
        "Cálcio": "Ca", "calcio": "Ca", "CÁLCIO": "Ca", "Ca": "Ca", "Calcium": "Ca",
        "Cálcio Ionizado": "Ca++", "calcio ionizado": "Ca++", "CÁLCIO IONIZADO": "Ca++", "Ca ionizado": "Ca++",
        "Magnésio": "Mg", "magnesio": "Mg", "MAGNÉSIO": "Mg", "Mg": "Mg", "Magnesium": "Mg",
        "Fósforo": "P", "fosforo": "P", "FÓSFORO": "P", "P": "P", "Phosphorus": "P",
        "Bicarbonato": "HCO3", "bicarbonato": "HCO3", "BICARBONATO": "HCO3",
        "CO2 Total": "CO2", "co2 total": "CO2", "CO2 TOTAL": "CO2", "Dióxido de Carbono": "CO2",

        # === PERFIL LIPÍDICO ULTRA EXPANDIDO ===
        "Colesterol Total": "CT", "colesterol total": "CT", "COLESTEROL TOTAL": "CT", "Total Cholesterol": "CT",
        "HDL Colesterol": "HDL", "hdl colesterol": "HDL", "HDL": "HDL", "HDL-C": "HDL", "Colesterol HDL": "HDL",
        "LDL Colesterol": "LDL", "ldl colesterol": "LDL", "LDL": "LDL", "LDL-C": "LDL", "Colesterol LDL": "LDL",
        "VLDL Colesterol": "VLDL", "vldl colesterol": "VLDL", "VLDL": "VLDL", "VLDL-C": "VLDL",
        "Triglicerídeos": "TG", "triglicerideos": "TG", "TRIGLICERÍDEOS": "TG", "Triglicérides": "TG", "Triglycerides": "TG",
        "Apolipoproteína A1": "ApoA1", "apolipoproteina a1": "ApoA1", "APO A1": "ApoA1", "Apo A-I": "ApoA1",
        "Apolipoproteína B": "ApoB", "apolipoproteina b": "ApoB", "APO B": "ApoB", "Apo B": "ApoB",
        "Lipoproteína A": "Lp(a)", "lipoproteina a": "Lp(a)", "LP(A)": "Lp(a)", "Lp-a": "Lp(a)",

        # === FUNÇÃO HEPÁTICA ULTRA EXPANDIDA ===
        "Alanina Aminotransferase": "ALT", "ALT": "ALT", "TGP": "ALT", "alanina aminotransferase": "ALT",
        "Transaminase Glutâmico Pirúvica": "ALT", "TRANS.GLUTÂMICO PIRÚVICA": "ALT", "TRANSAMINASE GLUTÂMICO PIRÚVICA": "ALT",
        "Aspartato Aminotransferase": "AST", "AST": "AST", "TGO": "AST", "aspartato aminotransferase": "AST",
        "Transaminase Glutâmico Oxalacética": "AST", "TRANS.GLUTÂMICO OXALACÉTICA": "AST", "TRANSAMINASE GLUTÂMICO OXALACÉTICA": "AST",
        "Fosfatase Alcalina": "FA", "fosfatase alcalina": "FA", "FOSFATASE ALCALINA": "FA", "Alkaline Phosphatase": "FA",
        "Gama-Glutamil Transferase": "GGT", "gama glutamil transferase": "GGT", "GGT": "GGT", "Gamma GT": "GGT",
        "Bilirrubina Total": "BT", "bilirrubina total": "BT", "BILIRRUBINA TOTAL": "BT", "Total Bilirubin": "BT",
        "Bilirrubina Direta": "BD", "bilirrubina direta": "BD", "BILIRRUBINA DIRETA": "BD", "Direct Bilirubin": "BD",
        "Bilirrubina Indireta": "BI", "bilirrubina indireta": "BI", "BILIRRUBINA INDIRETA": "BI", "Indirect Bilirubin": "BI",
        "Albumina": "Alb", "albumina": "Alb", "ALBUMINA": "Alb", "Albumin": "Alb",
        "Proteínas Totais": "PT", "proteinas totais": "PT", "PROTEÍNAS TOTAIS": "PT", "Total Proteins": "PT",
        "Globulinas": "Glob", "globulinas": "Glob", "GLOBULINAS": "Glob", "Globulins": "Glob",

        # === COAGULAÇÃO ULTRA EXPANDIDA ===
        "Tempo de Protrombina": "TP", "tempo protrombina": "TP", "TEMPO DE PROTROMBINA": "TP", "Prothrombin Time": "TP",
        "Tempo de Tromboplastina Parcial Ativada": "TTPA", "ttpa": "TTPA", "TTPA": "TTPA", "aPTT": "TTPA",
        "TEMPO DE TROMBOPLASTINA PARCIAL": "TTPA", "Activated Partial Thromboplastin Time": "TTPA",
        "INR": "INR", "inr": "INR", "I.N.R.": "INR", "International Normalized Ratio": "INR",
        "Fibrinogênio": "Fibr", "fibrinogenio": "Fibr", "FIBRINOGÊNIO": "Fibr", "Fibrinogen": "Fibr",
        "D-dímero": "DDim", "d dimero": "DDim", "D-DÍMERO": "DDim", "D-dimer": "DDim",
        "Antitrombina III": "AT3", "antitrombina iii": "AT3", "ANTITROMBINA III": "AT3", "Antithrombin III": "AT3",

        # === FUNÇÃO TIREOIDIANA ULTRA EXPANDIDA ===
        "Hormônio Tireoestimulante": "TSH", "TSH": "TSH", "tsh": "TSH", "Thyroid Stimulating Hormone": "TSH",
        "Tiroxina Livre": "T4L", "T4 Livre": "T4L", "t4 livre": "T4L", "T4L": "T4L", "Free T4": "T4L",
        "Triiodotironina Livre": "T3L", "T3 Livre": "T3L", "t3 livre": "T3L", "T3L": "T3L", "Free T3": "T3L",
        "Tiroxina Total": "T4T", "T4 Total": "T4T", "t4 total": "T4T", "Total T4": "T4T",
        "Triiodotironina Total": "T3T", "T3 Total": "T3T", "t3 total": "T3T", "Total T3": "T3T",
        "Anti-TPO": "Anti-TPO", "anti tpo": "Anti-TPO", "ANTI-TPO": "Anti-TPO", "Anticorpos Anti-TPO": "Anti-TPO",
        "Anti-Tireoglobulina": "Anti-TG", "anti tireoglobulina": "Anti-TG", "Anticorpos Anti-Tireoglobulina": "Anti-TG",
        "Tireoglobulina": "TG", "tireoglobulina": "TG", "TIREOGLOBULINA": "TG", "Thyroglobulin": "TG",

        # === MARCADORES CARDÍACOS ULTRA EXPANDIDOS ===
        "Troponina I": "TnI", "troponina i": "TnI", "TROPONINA I": "TnI", "Troponin I": "TnI",
        "Troponina T": "TnT", "troponina t": "TnT", "TROPONINA T": "TnT", "Troponin T": "TnT",
        "Troponina": "Tn", "troponina": "Tn", "TROPONINA": "Tn", "Troponin": "Tn",
        "Creatina Quinase": "CK", "creatina quinase": "CK", "CK": "CK", "Creatine Kinase": "CK",
        "CK-MB": "CK-MB", "ck mb": "CK-MB", "CK MB": "CK-MB", "Creatine Kinase MB": "CK-MB",
        "Lactato Desidrogenase": "LDH", "lactato desidrogenase": "LDH", "LDH": "LDH", "Lactate Dehydrogenase": "LDH",
        "Mioglobina": "Mio", "mioglobina": "Mio", "MIOGLOBINA": "Mio", "Myoglobin": "Mio",
        "BNP": "BNP", "bnp": "BNP", "B.N.P.": "BNP", "Brain Natriuretic Peptide": "BNP",
        "NT-proBNP": "NT-proBNP", "nt probnp": "NT-proBNP", "NT-PROBNP": "NT-proBNP", "N-terminal pro-BNP": "NT-proBNP",

        # === MARCADORES INFLAMATÓRIOS ULTRA EXPANDIDOS ===
        "Proteína C-reativa": "PCR", "proteina c reativa": "PCR", "PCR": "PCR", "C-Reactive Protein": "PCR",
        "PCR Ultra-sensível": "PCR-us", "pcr ultra sensivel": "PCR-us", "High Sensitivity CRP": "PCR-us",
        "Velocidade de Hemossedimentação": "VHS", "vhs": "VHS", "VHS": "VHS", "ESR": "VHS",
        "Procalcitonina": "PCT", "procalcitonina": "PCT", "PROCALCITONINA": "PCT", "Procalcitonin": "PCT",
        "Alfa-1-Antitripsina": "A1AT", "alfa 1 antitripsina": "A1AT", "Alpha-1 Antitrypsin": "A1AT",
        "Complemento C3": "C3", "complemento c3": "C3", "C3": "C3", "Complement C3": "C3",
        "Complemento C4": "C4", "complemento c4": "C4", "C4": "C4", "Complement C4": "C4",

        # === VITAMINAS E MINERAIS ULTRA EXPANDIDOS ===
        "Vitamina B12": "B12", "vitamina b12": "B12", "VITAMINA B12": "B12", "Cobalamina": "B12",
        "Ácido Fólico": "Folato", "acido folico": "Folato", "ÁCIDO FÓLICO": "Folato", "Folate": "Folato",
        "Vitamina D": "VitD", "vitamina d": "VitD", "VITAMINA D": "VitD", "25-OH Vitamina D": "VitD",
        "25-Hidroxivitamina D": "VitD", "25 oh vitamina d": "VitD", "Calcidiol": "VitD",
        "Vitamina D3": "VitD3", "vitamina d3": "VitD3", "VITAMINA D3": "VitD3", "Colecalciferol": "VitD3",
        "Ferro Sérico": "Fe", "ferro serico": "Fe", "FERRO SÉRICO": "Fe", "Ferro": "Fe", "Iron": "Fe",
        "Ferritina": "Ferr", "ferritina": "Ferr", "FERRITINA": "Ferr", "Ferritin": "Ferr",
        "Transferrina": "Transf", "transferrina": "Transf", "TRANSFERRINA": "Transf", "Transferrin": "Transf",
        "Saturação de Transferrina": "SatTransf", "saturacao transferrina": "SatTransf", "Transferrin Saturation": "SatTransf",
        "Capacidade Total de Ligação do Ferro": "CTLF", "ctlf": "CTLF", "CTLF": "CTLF", "TIBC": "CTLF",
        "Vitamina B1": "B1", "vitamina b1": "B1", "VITAMINA B1": "B1", "Tiamina": "B1",
        "Vitamina B6": "B6", "vitamina b6": "B6", "VITAMINA B6": "B6", "Piridoxina": "B6",
        "Vitamina C": "VitC", "vitamina c": "VitC", "VITAMINA C": "VitC", "Ácido Ascórbico": "VitC",
        "Vitamina E": "VitE", "vitamina e": "VitE", "VITAMINA E": "VitE", "Tocoferol": "VitE",
        "Vitamina A": "VitA", "vitamina a": "VitA", "VITAMINA A": "VitA", "Retinol": "VitA",
        "Zinco": "Zn", "zinco": "Zn", "ZINCO": "Zn", "Zinc": "Zn",
        "Cobre": "Cu", "cobre": "Cu", "COBRE": "Cu", "Copper": "Cu",
        "Selênio": "Se", "selenio": "Se", "SELÊNIO": "Se", "Selenium": "Se",

        # === PERFIL HORMONAL ULTRA EXPANDIDO ===
        "Cortisol": "Cort", "cortisol": "Cort", "CORTISOL": "Cort", "Hydrocortisone": "Cort",
        "Cortisol Livre Urinário": "CortU", "cortisol livre urinario": "CortU", "Free Cortisol": "CortU",
        "Insulina": "Ins", "insulina": "Ins", "INSULINA": "Ins", "Insulin": "Ins",
        "Peptídeo C": "PeptC", "peptideo c": "PeptC", "PEPTÍDEO C": "PeptC", "C-Peptide": "PeptC",
        "Testosterona Total": "TestoT", "testosterona total": "TestoT", "TESTOSTERONA TOTAL": "TestoT", "Total Testosterone": "TestoT",
        "Testosterona Livre": "TestoL", "testosterona livre": "TestoL", "TESTOSTERONA LIVRE": "TestoL", "Free Testosterone": "TestoL",
        "Prolactina": "Prol", "prolactina": "Prol", "PROLACTINA": "Prol", "Prolactin": "Prol",
        "Hormônio Luteinizante": "LH", "LH": "LH", "lh": "LH", "Luteinizing Hormone": "LH",
        "Hormônio Folículo-estimulante": "FSH", "FSH": "FSH", "fsh": "FSH", "Follicle Stimulating Hormone": "FSH",
        "Estradiol": "E2", "estradiol": "E2", "ESTRADIOL": "E2", "17β-Estradiol": "E2",
        "Progesterona": "Prog", "progesterona": "Prog", "PROGESTERONA": "Prog", "Progesterone": "Prog",
        "DHEA-S": "DHEA-S", "dhea s": "DHEA-S", "DHEA S": "DHEA-S", "Sulfato de Dehidroepiandrosterona": "DHEA-S",
        "SHBG": "SHBG", "shbg": "SHBG", "S.H.B.G.": "SHBG", "Sex Hormone Binding Globulin": "SHBG",
        "Hormônio de Crescimento": "GH", "GH": "GH", "gh": "GH", "Growth Hormone": "GH",
        "IGF-1": "IGF-1", "igf 1": "IGF-1", "IGF 1": "IGF-1", "Insulin-like Growth Factor 1": "IGF-1",
        "Paratormônio": "PTH", "PTH": "PTH", "pth": "PTH", "Parathyroid Hormone": "PTH",
        "Calcitonina": "Calc", "calcitonina": "Calc", "CALCITONINA": "Calc", "Calcitonin": "Calc",

        # === IMUNOLOGIA ULTRA EXPANDIDA ===
        "Imunoglobulina A": "IgA", "IgA": "IgA", "iga": "IgA", "Immunoglobulin A": "IgA",
        "Imunoglobulina G": "IgG", "IgG": "IgG", "igg": "IgG", "Immunoglobulin G": "IgG",
        "Imunoglobulina M": "IgM", "IgM": "IgM", "igm": "IgM", "Immunoglobulin M": "IgM",
        "Imunoglobulina E": "IgE", "IgE": "IgE", "ige": "IgE", "Immunoglobulin E": "IgE",
        "IgG1": "IgG1", "igg1": "IgG1", "IgG 1": "IgG1", "Immunoglobulin G1": "IgG1",
        "IgG2": "IgG2", "igg2": "IgG2", "IgG 2": "IgG2", "Immunoglobulin G2": "IgG2",
        "IgG3": "IgG3", "igg3": "IgG3", "IgG 3": "IgG3", "Immunoglobulin G3": "IgG3",
        "IgG4": "IgG4", "igg4": "IgG4", "IgG 4": "IgG4", "Immunoglobulin G4": "IgG4",

        # === FUNÇÃO PANCREÁTICA ===
        "Amilase": "Amil", "amilase": "Amil", "AMILASE": "Amil", "Amylase": "Amil",
        "Lipase": "Lip", "lipase": "Lip", "LIPASE": "Lip", "Lipase": "Lip",
        "Elastase Fecal": "Elast", "elastase fecal": "Elast", "ELASTASE FECAL": "Elast", "Fecal Elastase": "Elast",

        # === PERFIL METABÓLICO ===
        "Lactato": "Lac", "lactato": "Lac", "LACTATO": "Lac", "Lactic Acid": "Lac",
        "Ácido Lático": "Lac", "acido latico": "Lac", "ÁCIDO LÁTICO": "Lac",
        "Piruvato": "Pir", "piruvato": "Pir", "PIRUVATO": "Pir", "Pyruvate": "Pir",
        "Homocisteína": "Hcys", "homocisteina": "Hcys", "HOMOCISTEÍNA": "Hcys", "Homocysteine": "Hcys",
        "Ácido Fólico Eritrocitário": "FolErit", "acido folico eritrocitario": "FolErit", "RBC Folate": "FolErit",

        # === MARCADORES TUMORAIS ===
        "Antígeno Prostático Específico": "PSA", "PSA": "PSA", "psa": "PSA", "Prostate Specific Antigen": "PSA",
        "PSA Total": "PSA", "psa total": "PSA", "PSA TOTAL": "PSA", "Total PSA": "PSA",
        "PSA Livre": "PSAl", "psa livre": "PSAl", "PSA LIVRE": "PSAl", "Free PSA": "PSAl",
        "Antígeno Carcinoembrionário": "CEA", "CEA": "CEA", "cea": "CEA", "Carcinoembryonic Antigen": "CEA",
        "CA 125": "CA125", "ca 125": "CA125", "CA-125": "CA125", "Cancer Antigen 125": "CA125",
        "CA 15-3": "CA15-3", "ca 15 3": "CA15-3", "CA15.3": "CA15-3", "Cancer Antigen 15-3": "CA15-3",
        "CA 19-9": "CA19-9", "ca 19 9": "CA19-9", "CA19.9": "CA19-9", "Cancer Antigen 19-9": "CA19-9",
        "Alfa-fetoproteína": "AFP", "alfa fetoproteina": "AFP", "AFP": "AFP", "Alpha-fetoprotein": "AFP",
        "Beta-HCG": "βHCG", "beta hcg": "βHCG", "BETA HCG": "βHCG", "Human Chorionic Gonadotropin": "βHCG",

        # === PERFIL REUMATOLÓGICO ===
        "Fator Reumatóide": "FR", "fator reumatoide": "FR", "FR": "FR", "Rheumatoid Factor": "FR",
        "Anti-CCP": "Anti-CCP", "anti ccp": "Anti-CCP", "ANTI CCP": "Anti-CCP", "Cyclic Citrullinated Peptide": "Anti-CCP",
        "Anticorpos Antinucleares": "ANA", "ANA": "ANA", "ana": "ANA", "Antinuclear Antibodies": "ANA",
        "Anti-DNA": "Anti-DNA", "anti dna": "Anti-DNA", "ANTI DNA": "Anti-DNA", "Anti-double-stranded DNA": "Anti-DNA",
        "Anti-Sm": "Anti-Sm", "anti sm": "Anti-Sm", "ANTI SM": "Anti-Sm", "Anti-Smith": "Anti-Sm",
        "Anti-RNP": "Anti-RNP", "anti rnp": "Anti-RNP", "ANTI RNP": "Anti-RNP", "Anti-Ribonucleoprotein": "Anti-RNP",
        "Anti-Ro": "Anti-Ro", "anti ro": "Anti-Ro", "ANTI RO": "Anti-Ro", "Anti-SSA": "Anti-Ro",
        "Anti-La": "Anti-La", "anti la": "Anti-La", "ANTI LA": "Anti-La", "Anti-SSB": "Anti-La",
        "Anti-Scl70": "Anti-Scl70", "anti scl70": "Anti-Scl70", "ANTI SCL70": "Anti-Scl70", "Anti-Topoisomerase": "Anti-Scl70",
        "Anti-Centrômero": "Anti-Centr", "anti centromero": "Anti-Centr", "Anti-Centromere": "Anti-Centr",

        # === MARCADORES NEUROLÓGICOS ===
        "Proteína S100": "S100", "proteina s100": "S100", "S 100": "S100", "S-100 Protein": "S100",
        "Enolase Neuronal": "NSE", "enolase neuronal": "NSE", "NSE": "NSE", "Neuron-Specific Enolase": "NSE",
        "Proteína Tau": "Tau", "proteina tau": "Tau", "TAU": "Tau", "Tau Protein": "Tau",
        "Beta-Amilóide": "βAmil", "beta amiloide": "βAmil", "BETA AMILOIDE": "βAmil", "Amyloid Beta": "βAmil",

        # === ESTADO NUTRICIONAL ===
        "Pré-albumina": "PreAlb", "pre albumina": "PreAlb", "PRÉ ALBUMINA": "PreAlb", "Prealbumin": "PreAlb",
        "Colinesterase": "ColEst", "colinesterase": "ColEst", "COLINESTERASE": "ColEst", "Cholinesterase": "ColEst",

        # === OUTROS EXAMES IMPORTANTES ===
        "Proteína Sérica A Amilóide": "SAA", "SAA": "SAA", "saa": "SAA", "Serum Amyloid A": "SAA",
        "Anticorpos Anti-CCP": "Anti-CCP", "anticorpos anti ccp": "Anti-CCP"
    }

    RANGES = {
        # Hemograma Completo Expandido
        'hemoglobina': {'min': 13.5, 'max': 17.5, 'unit': 'g/dL', 'category': 'hemograma', 'abbrev': 'Hb'},
        'hematocrito': {'min': 41, 'max': 53, 'unit': '%', 'category': 'hemograma', 'abbrev': 'HT'},
        'eritrocitos': {'min': 4.0, 'max': 5.5, 'unit': 'milhões/mm³', 'category': 'hemograma', 'abbrev': 'Hem'},
        'leucocitos': {'min': 4000, 'max': 11000, 'unit': '/mm³', 'category': 'hemograma', 'abbrev': 'Leuco'},
        'neutrofilos': {'min': 1800, 'max': 7700, 'unit': '/mm³', 'category': 'hemograma', 'abbrev': 'N'},
        'linfocitos': {'min': 1000, 'max': 4800, 'unit': '/mm³', 'category': 'hemograma', 'abbrev': 'L'},
        'monocitos': {'min': 200, 'max': 1000, 'unit': '/mm³', 'category': 'hemograma', 'abbrev': 'Mono'},
        'eosinofilos': {'min': 50, 'max': 500, 'unit': '/mm³', 'category': 'hemograma', 'abbrev': 'Eos'},
        'basofilos': {'min': 0, 'max': 200, 'unit': '/mm³', 'category': 'hemograma', 'abbrev': 'Bas'},
        'plaquetas': {'min': 150000, 'max': 450000, 'unit': '/mm³', 'category': 'hemograma', 'abbrev': 'plaq'},
        'vcm': {'min': 80, 'max': 100, 'unit': 'fL', 'category': 'hemograma', 'abbrev': 'VCM'},
        'chcm': {'min': 31, 'max': 36, 'unit': '%', 'category': 'hemograma', 'abbrev': 'CHCM'},
        'rdw': {'min': 11.5, 'max': 14.5, 'unit': '%', 'category': 'hemograma', 'abbrev': 'RDW'},
        'hcm': {'min': 27, 'max': 32, 'unit': 'pg', 'category': 'hemograma', 'abbrev': 'HCM'},
        'reticulocitos': {'min': 0.5, 'max': 2.5, 'unit': '%', 'category': 'hemograma', 'abbrev': 'Retic'},

        # Bioquímica Básica Expandida
        'glicose': {'min': 70, 'max': 99, 'unit': 'mg/dL', 'category': 'bioquimica', 'abbrev': 'Gli'},
        'glicose_jejum': {'min': 70, 'max': 99, 'unit': 'mg/dL', 'category': 'bioquimica', 'abbrev': 'GliJ'},
        'glicose_pos_prandial': {'min': 0, 'max': 140, 'unit': 'mg/dL', 'category': 'bioquimica', 'abbrev': 'GliPP'},
        'hemoglobina_glicada': {'min': 0, 'max': 5.7, 'unit': '%', 'category': 'bioquimica', 'abbrev': 'HbA1c'},
        'frutosamina': {'min': 205, 'max': 285, 'unit': 'μmol/L', 'category': 'bioquimica', 'abbrev': 'Frut'},

        # Função Renal Expandida
        'creatinina': {'min': 0.6, 'max': 1.2, 'unit': 'mg/dL', 'category': 'renal', 'abbrev': 'Cr'},
        'ureia': {'min': 15, 'max': 45, 'unit': 'mg/dL', 'category': 'renal', 'abbrev': 'Ureia'},
        'acido_urico': {'min': 3.5, 'max': 7.2, 'unit': 'mg/dL', 'category': 'renal', 'abbrev': 'AU'},
        'clearance_creatinina': {'min': 90, 'max': 120, 'unit': 'mL/min', 'category': 'renal', 'abbrev': 'ClCr'},
        'cistatina_c': {'min': 0.53, 'max': 0.95, 'unit': 'mg/L', 'category': 'renal', 'abbrev': 'CisC'},
        'microalbuminuria': {'min': 0, 'max': 30, 'unit': 'mg/g', 'category': 'renal', 'abbrev': 'MAlb'},

        # Eletrólitos Expandidos
        'sodio': {'min': 135, 'max': 145, 'unit': 'mEq/L', 'category': 'eletrolitos', 'abbrev': 'Na'},
        'potassio': {'min': 3.5, 'max': 5.0, 'unit': 'mEq/L', 'category': 'eletrolitos', 'abbrev': 'K'},
        'cloro': {'min': 98, 'max': 107, 'unit': 'mEq/L', 'category': 'eletrolitos', 'abbrev': 'Cl'},
        'calcio': {'min': 8.5, 'max': 10.5, 'unit': 'mg/dL', 'category': 'eletrolitos', 'abbrev': 'Ca'},
        'calcio_ionizado': {'min': 4.6, 'max': 5.3, 'unit': 'mg/dL', 'category': 'eletrolitos', 'abbrev': 'Ca++'},
        'magnesio': {'min': 1.7, 'max': 2.2, 'unit': 'mg/dL', 'category': 'eletrolitos', 'abbrev': 'Mg'},
        'fosforo': {'min': 2.5, 'max': 4.5, 'unit': 'mg/dL', 'category': 'eletrolitos', 'abbrev': 'P'},
        'bicarbonato': {'min': 22, 'max': 28, 'unit': 'mEq/L', 'category': 'eletrolitos', 'abbrev': 'HCO3'},

        # Perfil Lipídico Expandido
        'colesterol_total': {'min': 0, 'max': 200, 'unit': 'mg/dL', 'category': 'lipidico', 'abbrev': 'CT'},
        'hdl': {'min': 40, 'max': 999, 'unit': 'mg/dL', 'category': 'lipidico', 'abbrev': 'HDL'},
        'ldl': {'min': 0, 'max': 130, 'unit': 'mg/dL', 'category': 'lipidico', 'abbrev': 'LDL'},
        'vldl': {'min': 5, 'max': 40, 'unit': 'mg/dL', 'category': 'lipidico', 'abbrev': 'VLDL'},
        'triglicerides': {'min': 0, 'max': 150, 'unit': 'mg/dL', 'category': 'lipidico', 'abbrev': 'TG'},
        'apolipoproteina_a1': {'min': 120, 'max': 160, 'unit': 'mg/dL', 'category': 'lipidico', 'abbrev': 'ApoA1'},
        'apolipoproteina_b': {'min': 60, 'max': 120, 'unit': 'mg/dL', 'category': 'lipidico', 'abbrev': 'ApoB'},
        'lipoproteina_a': {'min': 0, 'max': 30, 'unit': 'mg/dL', 'category': 'lipidico', 'abbrev': 'Lp(a)'},

        # Função Hepática Expandida
        'alt_tgp': {'min': 10, 'max': 49, 'unit': 'U/L', 'category': 'hepatico', 'abbrev': 'ALT'},
        'ast_tgo': {'min': 1, 'max': 34, 'unit': 'U/L', 'category': 'hepatico', 'abbrev': 'AST'},
        'fosfatase_alcalina': {'min': 44, 'max': 147, 'unit': 'U/L', 'category': 'hepatico', 'abbrev': 'FA'},
        'ggt': {'min': 9, 'max': 48, 'unit': 'U/L', 'category': 'hepatico', 'abbrev': 'GGT'},
        'bilirrubina_total': {'min': 0.3, 'max': 1.2, 'unit': 'mg/dL', 'category': 'hepatico', 'abbrev': 'BT'},
        'bilirrubina_direta': {'min': 0.0, 'max': 0.3, 'unit': 'mg/dL', 'category': 'hepatico', 'abbrev': 'BD'},
        'bilirrubina_indireta': {'min': 0.2, 'max': 0.8, 'unit': 'mg/dL', 'category': 'hepatico', 'abbrev': 'BI'},
        'albumina': {'min': 3.5, 'max': 5.0, 'unit': 'g/dL', 'category': 'hepatico', 'abbrev': 'Alb'},
        'proteinas_totais': {'min': 6.0, 'max': 8.3, 'unit': 'g/dL', 'category': 'hepatico', 'abbrev': 'PT'},
        'globulinas': {'min': 2.3, 'max': 3.5, 'unit': 'g/dL', 'category': 'hepatico', 'abbrev': 'Glob'},

        # Coagulação Expandida
        'tp': {'min': 11, 'max': 13.5, 'unit': 'segundos', 'category': 'coagulacao', 'abbrev': 'TP'},
        'ttpa': {'min': 25, 'max': 35, 'unit': 'segundos', 'category': 'coagulacao', 'abbrev': 'TTPA'},
        'inr': {'min': 0.8, 'max': 1.2, 'unit': '', 'category': 'coagulacao', 'abbrev': 'INR'},
        'fibrinogenio': {'min': 200, 'max': 400, 'unit': 'mg/dL', 'category': 'coagulacao', 'abbrev': 'Fibr'},
        'd_dimero': {'min': 0, 'max': 500, 'unit': 'ng/mL', 'category': 'coagulacao', 'abbrev': 'DDim'},
        'antitrombina_iii': {'min': 80, 'max': 120, 'unit': '%', 'category': 'coagulacao', 'abbrev': 'AT3'},

        # Função Tireoidiana Expandida
        'tsh': {'min': 0.4, 'max': 4.0, 'unit': 'mUI/L', 'category': 'tireoide', 'abbrev': 'TSH'},
        't4_livre': {'min': 0.8, 'max': 1.8, 'unit': 'ng/dL', 'category': 'tireoide', 'abbrev': 'T4L'},
        't3_livre': {'min': 2.3, 'max': 4.2, 'unit': 'pg/mL', 'category': 'tireoide', 'abbrev': 'T3L'},
        't4_total': {'min': 4.5, 'max': 12, 'unit': 'μg/dL', 'category': 'tireoide', 'abbrev': 'T4T'},
        't3_total': {'min': 80, 'max': 200, 'unit': 'ng/dL', 'category': 'tireoide', 'abbrev': 'T3T'},
        'anti_tpo': {'min': 0, 'max': 34, 'unit': 'UI/mL', 'category': 'tireoide', 'abbrev': 'Anti-TPO'},
        'anti_tireoglobulina': {'min': 0, 'max': 115, 'unit': 'UI/mL', 'category': 'tireoide', 'abbrev': 'Anti-TG'},
        'tireoglobulina': {'min': 1.4, 'max': 78, 'unit': 'ng/mL', 'category': 'tireoide', 'abbrev': 'TG'},

        # Marcadores Cardíacos Expandidos
        'troponina_i': {'min': 0, 'max': 0.04, 'unit': 'ng/mL', 'category': 'cardiaco', 'abbrev': 'TnI'},
        'troponina_t': {'min': 0, 'max': 0.01, 'unit': 'ng/mL', 'category': 'cardiaco', 'abbrev': 'TnT'},
        'ck': {'min': 30, 'max': 200, 'unit': 'U/L', 'category': 'cardiaco', 'abbrev': 'CK'},
        'ck_mb': {'min': 0, 'max': 6.3, 'unit': 'ng/mL', 'category': 'cardiaco', 'abbrev': 'CK-MB'},
        'ldh': {'min': 140, 'max': 280, 'unit': 'U/L', 'category': 'cardiaco', 'abbrev': 'LDH'},
        'mioglobina': {'min': 25, 'max': 72, 'unit': 'ng/mL', 'category': 'cardiaco', 'abbrev': 'Mio'},
        'bnp': {'min': 0, 'max': 100, 'unit': 'pg/mL', 'category': 'cardiaco', 'abbrev': 'BNP'},
        'nt_probnp': {'min': 0, 'max': 125, 'unit': 'pg/mL', 'category': 'cardiaco', 'abbrev': 'NT-proBNP'},

        # Marcadores Inflamatórios Expandidos
        'pcr': {'min': 0, 'max': 3.0, 'unit': 'mg/L', 'category': 'inflamatorio', 'abbrev': 'PCR'},
        'pcr_ultra_sensivel': {'min': 0, 'max': 3.0, 'unit': 'mg/L', 'category': 'inflamatorio', 'abbrev': 'PCR-us'},
        'vhs': {'min': 0, 'max': 20, 'unit': 'mm/h', 'category': 'inflamatorio', 'abbrev': 'VHS'},
        'procalcitonina': {'min': 0, 'max': 0.25, 'unit': 'ng/mL', 'category': 'inflamatorio', 'abbrev': 'PCT'},
        'alfa_1_antitripsina': {'min': 90, 'max': 200, 'unit': 'mg/dL', 'category': 'inflamatorio', 'abbrev': 'A1AT'},
        'complemento_c3': {'min': 90, 'max': 180, 'unit': 'mg/dL', 'category': 'inflamatorio', 'abbrev': 'C3'},
        'complemento_c4': {'min': 10, 'max': 40, 'unit': 'mg/dL', 'category': 'inflamatorio', 'abbrev': 'C4'},

        # Vitaminas e Minerais Expandidos
        'vitamina_b12': {'min': 200, 'max': 900, 'unit': 'pg/mL', 'category': 'vitaminas', 'abbrev': 'B12'},
        'acido_folico': {'min': 3.0, 'max': 17.0, 'unit': 'ng/mL', 'category': 'vitaminas', 'abbrev': 'Folato'},
        'vitamina_d': {'min': 30, 'max': 100, 'unit': 'ng/mL', 'category': 'vitaminas', 'abbrev': 'VitD'},
        'vitamina_d3': {'min': 30, 'max': 100, 'unit': 'ng/mL', 'category': 'vitaminas', 'abbrev': 'VitD3'},
        'ferro': {'min': 60, 'max': 170, 'unit': 'μg/dL', 'category': 'vitaminas', 'abbrev': 'Fe'},
        'ferritina': {'min': 15, 'max': 150, 'unit': 'ng/mL', 'category': 'vitaminas', 'abbrev': 'Ferr'},
        'transferrina': {'min': 200, 'max': 360, 'unit': 'mg/dL', 'category': 'vitaminas', 'abbrev': 'Transf'},
        'saturacao_transferrina': {'min': 20, 'max': 50, 'unit': '%', 'category': 'vitaminas', 'abbrev': 'SatTransf'},
        'vitamina_b1': {'min': 70, 'max': 180, 'unit': 'nmol/L', 'category': 'vitaminas', 'abbrev': 'B1'},
        'vitamina_b6': {'min': 20, 'max': 125, 'unit': 'nmol/L', 'category': 'vitaminas', 'abbrev': 'B6'},
        'vitamina_c': {'min': 23, 'max': 85, 'unit': 'μmol/L', 'category': 'vitaminas', 'abbrev': 'VitC'},
        'vitamina_e': {'min': 12, 'max': 42, 'unit': 'μmol/L', 'category': 'vitaminas', 'abbrev': 'VitE'},
        'vitamina_a': {'min': 1.05, 'max': 2.27, 'unit': 'μmol/L', 'category': 'vitaminas', 'abbrev': 'VitA'},
        'zinco': {'min': 70, 'max': 120, 'unit': 'μg/dL', 'category': 'vitaminas', 'abbrev': 'Zn'},
        'cobre': {'min': 70, 'max': 140, 'unit': 'μg/dL', 'category': 'vitaminas', 'abbrev': 'Cu'},
        'selenio': {'min': 70, 'max': 150, 'unit': 'μg/L', 'category': 'vitaminas', 'abbrev': 'Se'},

        # Perfil Hormonal Expandido
        'cortisol': {'min': 6.2, 'max': 19.4, 'unit': 'μg/dL', 'category': 'hormonios', 'abbrev': 'Cort'},
        'cortisol_livre_urinario': {'min': 10, 'max': 90, 'unit': 'μg/24h', 'category': 'hormonios', 'abbrev': 'CortU'},
        'insulina': {'min': 2.6, 'max': 24.9, 'unit': 'μU/mL', 'category': 'hormonios', 'abbrev': 'Ins'},
        'peptideo_c': {'min': 1.1, 'max': 4.4, 'unit': 'ng/mL', 'category': 'hormonios', 'abbrev': 'PeptC'},
        'testosterona_total': {'min': 280, 'max': 1100, 'unit': 'ng/dL', 'category': 'hormonios', 'abbrev': 'TestoT'},
        'testosterona_livre': {'min': 50, 'max': 210, 'unit': 'pg/mL', 'category': 'hormonios', 'abbrev': 'TestoL'},
        'prolactina': {'min': 4.0, 'max': 15.2, 'unit': 'ng/mL', 'category': 'hormonios', 'abbrev': 'Prol'},
        'lh': {'min': 1.7, 'max': 8.6, 'unit': 'mUI/mL', 'category': 'hormonios', 'abbrev': 'LH'},
        'fsh': {'min': 1.5, 'max': 12.4, 'unit': 'mUI/mL', 'category': 'hormonios', 'abbrev': 'FSH'},
        'estradiol': {'min': 7.6, 'max': 42.6, 'unit': 'pg/mL', 'category': 'hormonios', 'abbrev': 'E2'},
        'progesterona': {'min': 0.2, 'max': 1.4, 'unit': 'ng/mL', 'category': 'hormonios', 'abbrev': 'Prog'},
        'dhea_s': {'min': 80, 'max': 560, 'unit': 'μg/dL', 'category': 'hormonios', 'abbrev': 'DHEA-S'},
        'shbg': {'min': 18, 'max': 54, 'unit': 'nmol/L', 'category': 'hormonios', 'abbrev': 'SHBG'},
        'gh': {'min': 0, 'max': 10, 'unit': 'ng/mL', 'category': 'hormonios', 'abbrev': 'GH'},
        'igf1': {'min': 115, 'max': 307, 'unit': 'ng/mL', 'category': 'hormonios', 'abbrev': 'IGF-1'},
        'pth': {'min': 15, 'max': 65, 'unit': 'pg/mL', 'category': 'hormonios', 'abbrev': 'PTH'},
        'calcitonina': {'min': 0, 'max': 10, 'unit': 'pg/mL', 'category': 'hormonios', 'abbrev': 'Calc'},

        # Imunologia Expandida
        'iga': {'min': 70, 'max': 400, 'unit': 'mg/dL', 'category': 'imunologia', 'abbrev': 'IgA'},
        'igg': {'min': 700, 'max': 1600, 'unit': 'mg/dL', 'category': 'imunologia', 'abbrev': 'IgG'},
        'igm': {'min': 40, 'max': 230, 'unit': 'mg/dL', 'category': 'imunologia', 'abbrev': 'IgM'},
        'ige': {'min': 0, 'max': 100, 'unit': 'UI/mL', 'category': 'imunologia', 'abbrev': 'IgE'},
        'igg1': {'min': 400, 'max': 900, 'unit': 'mg/dL', 'category': 'imunologia', 'abbrev': 'IgG1'},
        'igg2': {'min': 200, 'max': 600, 'unit': 'mg/dL', 'category': 'imunologia', 'abbrev': 'IgG2'},
        'igg3': {'min': 50, 'max': 150, 'unit': 'mg/dL', 'category': 'imunologia', 'abbrev': 'IgG3'},
        'igg4': {'min': 5, 'max': 125, 'unit': 'mg/dL', 'category': 'imunologia', 'abbrev': 'IgG4'},

        # Função Pancreática
        'amilase': {'min': 28, 'max': 100, 'unit': 'U/L', 'category': 'pancreatico', 'abbrev': 'Amil'},
        'lipase': {'min': 10, 'max': 140, 'unit': 'U/L', 'category': 'pancreatico', 'abbrev': 'Lip'},
        'elastase_fecal': {'min': 200, 'max': 999, 'unit': 'μg/g', 'category': 'pancreatico', 'abbrev': 'Elast'},

        # Perfil Metabólico
        'lactato': {'min': 0.5, 'max': 2.2, 'unit': 'mmol/L', 'category': 'metabolico', 'abbrev': 'Lac'},
        'piruvato': {'min': 0.03, 'max': 0.10, 'unit': 'mmol/L', 'category': 'metabolico', 'abbrev': 'Pir'},
        'homocisteina': {'min': 5, 'max': 15, 'unit': 'μmol/L', 'category': 'metabolico', 'abbrev': 'Hcys'},
        'acido_folico_eritrocitario': {'min': 280, 'max': 903, 'unit': 'ng/mL', 'category': 'metabolico', 'abbrev': 'FolErit'},

        # Marcadores Tumorais
        'psa_total': {'min': 0, 'max': 4.0, 'unit': 'ng/mL', 'category': 'oncologico', 'abbrev': 'PSA'},
        'psa_livre': {'min': 0, 'max': 1.0, 'unit': 'ng/mL', 'category': 'oncologico', 'abbrev': 'PSAl'},
        'cea': {'min': 0, 'max': 5.0, 'unit': 'ng/mL', 'category': 'oncologico', 'abbrev': 'CEA'},
        'ca_125': {'min': 0, 'max': 35, 'unit': 'U/mL', 'category': 'oncologico', 'abbrev': 'CA125'},
        'ca_153': {'min': 0, 'max': 25, 'unit': 'U/mL', 'category': 'oncologico', 'abbrev': 'CA15-3'},
        'ca_199': {'min': 0, 'max': 37, 'unit': 'U/mL', 'category': 'oncologico', 'abbrev': 'CA19-9'},
        'alfa_fetoproteina': {'min': 0, 'max': 10, 'unit': 'ng/mL', 'category': 'oncologico', 'abbrev': 'AFP'},
        'beta_hcg': {'min': 0, 'max': 5, 'unit': 'mUI/mL', 'category': 'oncologico', 'abbrev': 'βHCG'},

        # Perfil Reumatológico
        'fator_reumatoide': {'min': 0, 'max': 20, 'unit': 'UI/mL', 'category': 'reumatologico', 'abbrev': 'FR'},
        'anti_ccp': {'min': 0, 'max': 20, 'unit': 'U/mL', 'category': 'reumatologico', 'abbrev': 'Anti-CCP'},
        'ana': {'min': 0, 'max': 80, 'unit': 'título', 'category': 'reumatologico', 'abbrev': 'ANA'},
        'anti_dna': {'min': 0, 'max': 30, 'unit': 'UI/mL', 'category': 'reumatologico', 'abbrev': 'Anti-DNA'},
        'anti_sm': {'min': 0, 'max': 20, 'unit': 'U/mL', 'category': 'reumatologico', 'abbrev': 'Anti-Sm'},
        'anti_rnp': {'min': 0, 'max': 20, 'unit': 'U/mL', 'category': 'reumatologico', 'abbrev': 'Anti-RNP'},
        'anti_ro': {'min': 0, 'max': 20, 'unit': 'U/mL', 'category': 'reumatologico', 'abbrev': 'Anti-Ro'},
        'anti_la': {'min': 0, 'max': 20, 'unit': 'U/mL', 'category': 'reumatologico', 'abbrev': 'Anti-La'},
        'anti_scl70': {'min': 0, 'max': 20, 'unit': 'U/mL', 'category': 'reumatologico', 'abbrev': 'Anti-Scl70'},
        'anti_centromero': {'min': 0, 'max': 20, 'unit': 'U/mL', 'category': 'reumatologico', 'abbrev': 'Anti-Centr'},

        # Marcadores Neurológicos
        'proteina_s100': {'min': 0, 'max': 0.15, 'unit': 'μg/L', 'category': 'neurológico', 'abbrev': 'S100'},
        'enolase_neuronal': {'min': 0, 'max': 12.5, 'unit': 'ng/mL', 'category': 'neurológico', 'abbrev': 'NSE'},
        'tau_protein': {'min': 0, 'max': 300, 'unit': 'pg/mL', 'category': 'neurológico', 'abbrev': 'Tau'},
        'beta_amiloide': {'min': 500, 'max': 1500, 'unit': 'pg/mL', 'category': 'neurológico', 'abbrev': 'βAmil'},

        # Estado Nutricional
        'pre_albumina': {'min': 20, 'max': 40, 'unit': 'mg/dL', 'category': 'nutricional', 'abbrev': 'PreAlb'},
        'transferrina': {'min': 200, 'max': 360, 'unit': 'mg/dL', 'category': 'nutricional', 'abbrev': 'Transf'},
        'proteina_c_reativa': {'min': 0, 'max': 3.0, 'unit': 'mg/L', 'category': 'nutricional', 'abbrev': 'PCR'},
        'colina_esterase': {'min': 5300, 'max': 12900, 'unit': 'U/L', 'category': 'nutricional', 'abbrev': 'ColEst'},
    }
    
    @classmethod
    def get_reference(cls, test_name: str) -> Optional[Dict]:
        """Get reference range for a test"""
        return cls.RANGES.get(test_name.lower().replace(' ', '_'))
    
    @classmethod
    def check_status(cls, test_name: str, value: float) -> str:
        """Check if value is normal, high, or low"""
        ref = cls.get_reference(test_name)
        if not ref:
            return "unknown"
        
        if value < ref['min']:
            return "low"
        elif value > ref['max']:
            return "high"
        else:
            return "normal"

class AdvancedAnalytics:
    """Advanced analytics and visualization for lab results"""

    @staticmethod
    def extract_historical_data(text: str) -> Dict[str, List[Tuple[str, float]]]:
        """Extract historical lab data from text"""
        historical_data = {}

        # Pattern to match historical data: Test_name date: value
        for test_name, abbrev in ReferenceRanges.EXAMES_DICT.items():
            pattern = rf"{test_name}.*?(\d{{2}}/\d{{2}}/\d{{4}})\s*:\s*([\d,.]+)"
            matches = re.findall(pattern, text, re.IGNORECASE | re.DOTALL)
            if matches:
                historical_data[abbrev] = [(date, float(value.replace(',', '.'))) for date, value in matches]

        return historical_data

    @staticmethod
    def create_trends_dataframe(historical_data: Dict) -> pd.DataFrame:
        """Create DataFrame from historical data for analysis"""
        df_data = []
        for test_name, values in historical_data.items():
            for date_str, value in values:
                try:
                    date_obj = datetime.strptime(date_str, '%d/%m/%Y')
                    df_data.append({
                        'Exame': test_name,
                        'Data': date_obj,
                        'Valor': value
                    })
                except ValueError:
                    continue

        df = pd.DataFrame(df_data)
        return df.sort_values(['Exame', 'Data'], ascending=[True, False]) if not df.empty else df

    @staticmethod
    def generate_trends_chart(df: pd.DataFrame, selected_tests: List[str]) -> go.Figure:
        """Generate interactive trends chart"""
        if df.empty or not selected_tests:
            return go.Figure()

        fig = make_subplots(
            rows=len(selected_tests),
            cols=1,
            subplot_titles=selected_tests,
            vertical_spacing=0.08
        )

        for i, test in enumerate(selected_tests, start=1):
            test_data = df[df['Exame'] == test]
            if not test_data.empty:
                # Add main trend line
                fig.add_trace(
                    go.Scatter(
                        x=test_data['Data'],
                        y=test_data['Valor'],
                        mode='lines+markers',
                        name=test,
                        line=dict(width=3),
                        marker=dict(size=8)
                    ),
                    row=i, col=1
                )

                # Add reference range lines
                test_key = test.lower().replace(' ', '_')
                ref = ReferenceRanges.get_reference(test_key)
                if ref:
                    # Min reference line
                    fig.add_hline(
                        y=ref['min'],
                        line_dash="dash",
                        line_color="red",
                        annotation_text=f"Min: {ref['min']} {ref['unit']}",
                        row=i, col=1
                    )
                    # Max reference line
                    fig.add_hline(
                        y=ref['max'],
                        line_dash="dash",
                        line_color="red",
                        annotation_text=f"Max: {ref['max']} {ref['unit']}",
                        row=i, col=1
                    )

        fig.update_layout(
            height=300 * len(selected_tests),
            title_text="📈 Tendências dos Exames Laboratoriais",
            showlegend=False,
            template="plotly_white"
        )

        return fig

    @staticmethod
    def generate_correlation_heatmap(df: pd.DataFrame) -> str:
        """Generate correlation heatmap as base64 image"""
        if df.empty:
            return ""

        try:
            # Pivot data for correlation analysis
            pivot_df = df.pivot(index='Data', columns='Exame', values='Valor')

            if pivot_df.shape[1] < 2:  # Need at least 2 variables for correlation
                return ""

            # Calculate correlation matrix
            corr_matrix = pivot_df.corr()

            # Create heatmap
            plt.figure(figsize=(12, 10))
            sns.heatmap(
                corr_matrix,
                annot=True,
                cmap='RdYlBu_r',
                vmin=-1,
                vmax=1,
                center=0,
                square=True,
                fmt='.2f'
            )
            plt.title('🔥 Matriz de Correlação entre Exames', fontsize=16, fontweight='bold')
            plt.tight_layout()

            # Convert to base64
            buf = BytesIO()
            plt.savefig(buf, format="png", dpi=300, bbox_inches='tight')
            buf.seek(0)
            img_base64 = base64.b64encode(buf.getvalue()).decode()
            plt.close()

            return img_base64

        except Exception as e:
            st.error(f"Erro ao gerar matriz de correlação: {str(e)}")
            return ""

    @staticmethod
    def calculate_lab_score(lab_results: List[LabResult]) -> Dict[str, Any]:
        """Calculate overall lab health score"""
        if not lab_results:
            return {"score": 0, "grade": "N/A", "summary": "Sem dados"}

        total_tests = len(lab_results)
        normal_tests = sum(1 for result in lab_results if result.status == "normal")
        abnormal_tests = total_tests - normal_tests

        # Calculate score (0-100)
        score = (normal_tests / total_tests) * 100 if total_tests > 0 else 0

        # Determine grade
        if score >= 90:
            grade = "A+ Excelente"
            color = "green"
        elif score >= 80:
            grade = "A Muito Bom"
            color = "lightgreen"
        elif score >= 70:
            grade = "B Bom"
            color = "yellow"
        elif score >= 60:
            grade = "C Regular"
            color = "orange"
        else:
            grade = "D Atenção"
            color = "red"

        return {
            "score": round(score, 1),
            "grade": grade,
            "color": color,
            "normal_tests": normal_tests,
            "abnormal_tests": abnormal_tests,
            "total_tests": total_tests,
            "summary": f"{normal_tests}/{total_tests} exames normais"
        }

class SmartFormatter:
    """Smart formatting with advanced features"""

    @staticmethod
    def format_for_medical_record(lab_results: List[LabResult], exam_date: str,
                                 include_reference: bool = False,
                                 style: str = "compact") -> str:
        """Ultra compact formatting with standardized categories and abbreviated exams"""
        if not lab_results:
            return f"Labs ({exam_date}): / / /"

        # Category mapping with standardized names
        category_names = {
            'hemograma': 'Hemograma',
            'bioquimica': 'Bioquímica',
            'hepatico': 'Hepático',
            'tireoide': 'Tireoide',
            'lipidico': 'Lipídico',
            'eletrolitos': 'Eletrólitos',
            'coagulacao': 'Coagulação',
            'cardiaco': 'Cardíaco',
            'inflamatorio': 'Inflamatório',
            'vitaminas': 'Vitaminas',
            'hormonios': 'Hormônios',
            'imunologia': 'Imunologia'
        }

        # Group by category
        categories = {}
        for result in lab_results:
            if result.category not in categories:
                categories[result.category] = []
            categories[result.category].append(result)

        formatted_parts = []

        if style == "compact" or style == "ultra_standardized":
            # Ultra compact style with slashes
            for category, results in categories.items():
                if not results:
                    continue

                category_name = category_names.get(category, category.title())
                parts = []

                for result in results:
                    # Get abbreviation from reference ranges
                    ref = ReferenceRanges.get_reference(result.test_name.lower().replace(' ', '_'))
                    abbrev = ref.get('abbrev', result.test_name[:3]) if ref else result.test_name[:3]

                    # Ultra compact format: /abbrev value unit/
                    value_clean = result.value.replace(',', '.')
                    unit_clean = result.unit.replace('/', '').replace(' ', '') if result.unit else ''

                    # Status indicators for abnormal values
                    status_indicator = ""
                    if include_reference and result.status != "normal":
                        if result.status == "high":
                            status_indicator = "↑"
                        elif result.status == "low":
                            status_indicator = "↓"

                    # Remove redundant units from value if already present
                    if unit_clean and unit_clean.lower() in value_clean.lower():
                        formatted_value = f"/{abbrev} {value_clean}{status_indicator}/"
                    else:
                        formatted_value = f"/{abbrev} {value_clean}{unit_clean}{status_indicator}/"

                    parts.append(formatted_value)

                if parts:
                    # Format: Category: /exam1 value1/ /exam2 value2/ /exam3 value3/
                    category_line = f"{category_name}: {' '.join(parts)}"
                    formatted_parts.append(category_line)

        elif style == "detailed":
            # Detailed style with categories separated
            for category, results in categories.items():
                category_name = category_names.get(category, category.title())
                category_parts = [f"\n{category_name}:"]

                for result in results:
                    ref = ReferenceRanges.get_reference(result.test_name.lower().replace(' ', '_'))
                    abbrev = ref.get('abbrev', result.test_name) if ref else result.test_name

                    status_indicator = ""
                    if result.status == "high":
                        status_indicator = " ↑"
                    elif result.status == "low":
                        status_indicator = " ↓"

                    value_line = f"  • /{abbrev}/: {result.value} {result.unit}{status_indicator}"
                    if include_reference:
                        value_line += f" (Ref: {result.reference_range})"

                    category_parts.append(value_line)

                formatted_parts.append('\n'.join(category_parts))



        # Combine all parts
        if style == "compact" or style == "ultra_standardized":
            result_text = " ".join(formatted_parts)
            return f"Labs ({exam_date}): {result_text}"
        else:
            return f"Labs ({exam_date}):" + '\n'.join(formatted_parts)

class LabTextParser:
    """Advanced text parser for lab results"""
    
    def __init__(self):
        self.patterns = self._create_patterns()
    
    def _create_patterns(self) -> Dict[str, re.Pattern]:
        """Create ultra comprehensive regex patterns for all lab tests with enhanced text recognition"""
        patterns = {}

        # Ultra flexible patterns for lab values with multiple formats
        value_pattern = r'([0-9]+[.,]?[0-9]*(?:\s*[x×]\s*10\^?[0-9]+)?)'
        separator_pattern = r'[:\s=\-\.\(\)]*'
        unit_separator = r'[\s\(\)]*'
        optional_unit = r'(?:\s*[a-zA-Z/%³²⁺⁻μ]+)?'

        # === HEMOGRAMA COMPLETO ULTRA EXPANDIDO ===
        patterns['hemoglobina'] = re.compile(rf'(?:hemoglobina|hb|hemoglobin|hgb){separator_pattern}{value_pattern}{unit_separator}(?:g/dl|g/dL|g\/dl|gdl|g\.dl)?', re.IGNORECASE)
        patterns['hematocrito'] = re.compile(rf'(?:hematócrito|hematocrito|ht|hct|htc){separator_pattern}{value_pattern}{unit_separator}(?:%|percent|pct)?', re.IGNORECASE)
        patterns['eritrocitos'] = re.compile(rf'(?:eritrócitos|eritrocitos|hemácias|hemacias|rbc|red blood cells){separator_pattern}{value_pattern}{unit_separator}(?:milhões/mm³|milhoes/mm3|x10\^?6/μL|/mm³|mill/mm3)?', re.IGNORECASE)
        patterns['leucocitos'] = re.compile(rf'(?:leucócitos|leucocitos|wbc|white blood cells|globulos brancos){separator_pattern}{value_pattern}{unit_separator}(?:/mm³|/mm3|x10\^?3/μL|mil/mm3)?', re.IGNORECASE)
        patterns['neutrofilos'] = re.compile(rf'(?:neutrófilos|neutrofilos|neutrophils|segmentados|segs){separator_pattern}{value_pattern}{unit_separator}(?:/mm³|/mm3|%|percent)?', re.IGNORECASE)
        patterns['linfocitos'] = re.compile(rf'(?:linfócitos|linfocitos|lymphocytes|linf){separator_pattern}{value_pattern}{unit_separator}(?:/mm³|/mm3|%|percent)?', re.IGNORECASE)
        patterns['monocitos'] = re.compile(rf'(?:monócitos|monocitos|monocytes|mono){separator_pattern}{value_pattern}{unit_separator}(?:/mm³|/mm3|%|percent)?', re.IGNORECASE)
        patterns['eosinofilos'] = re.compile(rf'(?:eosinófilos|eosinofilos|eosinophils|eos){separator_pattern}{value_pattern}{unit_separator}(?:/mm³|/mm3|%|percent)?', re.IGNORECASE)
        patterns['basofilos'] = re.compile(rf'(?:basófilos|basofilos|basophils|bas){separator_pattern}{value_pattern}{unit_separator}(?:/mm³|/mm3|%|percent)?', re.IGNORECASE)
        patterns['plaquetas'] = re.compile(rf'(?:plaquetas|platelets|plt|plaq){separator_pattern}{value_pattern}{unit_separator}(?:/mm³|/mm3|x10\^?3/μL|mil/mm3)?', re.IGNORECASE)
        patterns['vcm'] = re.compile(rf'(?:vcm|mcv|volume corpuscular médio|volume corpuscular medio){separator_pattern}{value_pattern}{unit_separator}(?:fl|fL|femtolitros)?', re.IGNORECASE)
        patterns['chcm'] = re.compile(rf'(?:chcm|mchc|concentração.*hemoglobina|concentracao.*hemoglobina){separator_pattern}{value_pattern}{unit_separator}(?:g/dl|g/dL|%|percent)?', re.IGNORECASE)
        patterns['rdw'] = re.compile(rf'(?:rdw|amplitude.*distribuição|amplitude.*distribuicao){separator_pattern}{value_pattern}{unit_separator}(?:%|percent)?', re.IGNORECASE)
        patterns['hcm'] = re.compile(rf'(?:hcm|mch|hemoglobina corpuscular média|hemoglobina corpuscular media){separator_pattern}{value_pattern}{unit_separator}(?:pg|picogramas)?', re.IGNORECASE)
        patterns['reticulocitos'] = re.compile(rf'(?:reticulócitos|reticulocitos|reticulocytes|retic){separator_pattern}{value_pattern}{unit_separator}(?:%|percent)?', re.IGNORECASE)

        # === BIOQUÍMICA SÉRICA ULTRA EXPANDIDA ===
        patterns['glicose'] = re.compile(rf'(?:glicose|glucose|gli|glicemia){separator_pattern}{value_pattern}{unit_separator}(?:mg/dl|mg/dL|mmol/L|mgdl)?', re.IGNORECASE)
        patterns['glicose_jejum'] = re.compile(rf'(?:glicose.*jejum|glucose.*fasting|glicemia.*jejum){separator_pattern}{value_pattern}{unit_separator}(?:mg/dl|mg/dL|mmol/L)?', re.IGNORECASE)
        patterns['glicose_pos_prandial'] = re.compile(rf'(?:glicose.*pós.*prandial|glucose.*post.*prandial|glicemia.*pos.*prandial){separator_pattern}{value_pattern}{unit_separator}(?:mg/dl|mg/dL|mmol/L)?', re.IGNORECASE)
        patterns['hemoglobina_glicada'] = re.compile(rf'(?:hemoglobina glicada|hba1c|a1c|hb glicada){separator_pattern}{value_pattern}{unit_separator}(?:%|percent)?', re.IGNORECASE)
        patterns['frutosamina'] = re.compile(rf'(?:frutosamina|fructosamine){separator_pattern}{value_pattern}{unit_separator}(?:μmol/L|umol/L)?', re.IGNORECASE)

        # === FUNÇÃO RENAL ULTRA EXPANDIDA ===
        patterns['creatinina'] = re.compile(rf'(?:creatinina|creatinine|cr){separator_pattern}{value_pattern}{unit_separator}(?:mg/dl|mg/dL|μmol/L|mgdl)?', re.IGNORECASE)
        patterns['ureia'] = re.compile(rf'(?:uréia|ureia|urea|bun){separator_pattern}{value_pattern}{unit_separator}(?:mg/dl|mg/dL|mmol/L|mgdl)?', re.IGNORECASE)
        patterns['acido_urico'] = re.compile(rf'(?:ácido úrico|acido urico|uric acid|urato){separator_pattern}{value_pattern}{unit_separator}(?:mg/dl|mg/dL|μmol/L|mgdl)?', re.IGNORECASE)
        patterns['clearance_creatinina'] = re.compile(rf'(?:clearance.*creatinina|depuração.*creatinina|creatinine clearance){separator_pattern}{value_pattern}{unit_separator}(?:ml/min|mL/min)?', re.IGNORECASE)
        patterns['cistatina_c'] = re.compile(rf'(?:cistatina c|cystatin c){separator_pattern}{value_pattern}{unit_separator}(?:mg/l|mg/L)?', re.IGNORECASE)
        patterns['microalbuminuria'] = re.compile(rf'(?:microalbuminúria|microalbuminuria|microalbumin){separator_pattern}{value_pattern}{unit_separator}(?:mg/g|mg/24h)?', re.IGNORECASE)

        # === ELETRÓLITOS ULTRA EXPANDIDOS ===
        patterns['sodio'] = re.compile(rf'(?:sódio|sodio|sodium|na\+?){separator_pattern}{value_pattern}{unit_separator}(?:meq/l|mEq/L|mmol/L)?', re.IGNORECASE)
        patterns['potassio'] = re.compile(rf'(?:potássio|potassio|potassium|k\+?){separator_pattern}{value_pattern}{unit_separator}(?:meq/l|mEq/L|mmol/L)?', re.IGNORECASE)
        patterns['cloro'] = re.compile(rf'(?:cloro|chloride|cl\-?){separator_pattern}{value_pattern}{unit_separator}(?:meq/l|mEq/L|mmol/L)?', re.IGNORECASE)
        patterns['calcio'] = re.compile(rf'(?:cálcio|calcio|calcium|ca\+?\+?){separator_pattern}{value_pattern}{unit_separator}(?:mg/dl|mg/dL|mmol/L)?', re.IGNORECASE)
        patterns['calcio_ionizado'] = re.compile(rf'(?:cálcio ionizado|calcio ionizado|ionized calcium|ca\+\+){separator_pattern}{value_pattern}{unit_separator}(?:mg/dl|mg/dL|mmol/L)?', re.IGNORECASE)
        patterns['magnesio'] = re.compile(rf'(?:magnésio|magnesio|magnesium|mg\+?\+?){separator_pattern}{value_pattern}{unit_separator}(?:mg/dl|mg/dL|mmol/L)?', re.IGNORECASE)
        patterns['fosforo'] = re.compile(rf'(?:fósforo|fosforo|phosphorus|p){separator_pattern}{value_pattern}{unit_separator}(?:mg/dl|mg/dL|mmol/L)?', re.IGNORECASE)
        patterns['bicarbonato'] = re.compile(rf'(?:bicarbonato|bicarbonate|hco3){separator_pattern}{value_pattern}{unit_separator}(?:meq/l|mEq/L|mmol/L)?', re.IGNORECASE)

        # === PERFIL LIPÍDICO ULTRA EXPANDIDO ===
        patterns['colesterol_total'] = re.compile(rf'(?:colesterol total|total cholesterol|ct|colesterol){separator_pattern}{value_pattern}{unit_separator}(?:mg/dl|mg/dL|mmol/L)?', re.IGNORECASE)
        patterns['hdl'] = re.compile(rf'(?:hdl|hdl colesterol|hdl cholesterol|colesterol hdl){separator_pattern}{value_pattern}{unit_separator}(?:mg/dl|mg/dL|mmol/L)?', re.IGNORECASE)
        patterns['ldl'] = re.compile(rf'(?:ldl|ldl colesterol|ldl cholesterol|colesterol ldl){separator_pattern}{value_pattern}{unit_separator}(?:mg/dl|mg/dL|mmol/L)?', re.IGNORECASE)
        patterns['vldl'] = re.compile(rf'(?:vldl|vldl colesterol|vldl cholesterol|colesterol vldl){separator_pattern}{value_pattern}{unit_separator}(?:mg/dl|mg/dL|mmol/L)?', re.IGNORECASE)
        patterns['triglicerides'] = re.compile(rf'(?:triglicerídeos|triglicerides|triglycerides|tg|triglicérides){separator_pattern}{value_pattern}{unit_separator}(?:mg/dl|mg/dL|mmol/L)?', re.IGNORECASE)
        patterns['apolipoproteina_a1'] = re.compile(rf'(?:apolipoproteína a1|apolipoproteina a1|apo a1|apolipoprotein a1){separator_pattern}{value_pattern}{unit_separator}(?:mg/dl|mg/dL)?', re.IGNORECASE)
        patterns['apolipoproteina_b'] = re.compile(rf'(?:apolipoproteína b|apolipoproteina b|apo b|apolipoprotein b){separator_pattern}{value_pattern}{unit_separator}(?:mg/dl|mg/dL)?', re.IGNORECASE)
        patterns['lipoproteina_a'] = re.compile(rf'(?:lipoproteína a|lipoproteina a|lp\(a\)|lipoprotein a){separator_pattern}{value_pattern}{unit_separator}(?:mg/dl|mg/dL)?', re.IGNORECASE)

        # === FUNÇÃO HEPÁTICA ULTRA EXPANDIDA ===
        patterns['alt_tgp'] = re.compile(rf'(?:alt|tgp|alanina aminotransferase|alat|transaminase.*pirúvica|transaminase.*piruv){separator_pattern}{value_pattern}{unit_separator}(?:u/l|U/L|iu/l|ui/l)?', re.IGNORECASE)
        patterns['ast_tgo'] = re.compile(rf'(?:ast|tgo|aspartato aminotransferase|asat|transaminase.*oxalacética|transaminase.*oxalac){separator_pattern}{value_pattern}{unit_separator}(?:u/l|U/L|iu/l|ui/l)?', re.IGNORECASE)
        patterns['fosfatase_alcalina'] = re.compile(rf'(?:fosfatase alcalina|alkaline phosphatase|fa|alp|fosf.*alc){separator_pattern}{value_pattern}{unit_separator}(?:u/l|U/L|iu/l|ui/l)?', re.IGNORECASE)
        patterns['ggt'] = re.compile(rf'(?:ggt|gama.*glutamil|gamma.*glutamyl|γ.*gt){separator_pattern}{value_pattern}{unit_separator}(?:u/l|U/L|iu/l|ui/l)?', re.IGNORECASE)
        patterns['bilirrubina_total'] = re.compile(rf'(?:bilirrubina total|total bilirubin|bt|bili.*total){separator_pattern}{value_pattern}{unit_separator}(?:mg/dl|mg/dL|μmol/L)?', re.IGNORECASE)
        patterns['bilirrubina_direta'] = re.compile(rf'(?:bilirrubina direta|direct bilirubin|bd|bili.*direta){separator_pattern}{value_pattern}{unit_separator}(?:mg/dl|mg/dL|μmol/L)?', re.IGNORECASE)
        patterns['bilirrubina_indireta'] = re.compile(rf'(?:bilirrubina indireta|indirect bilirubin|bi|bili.*indireta){separator_pattern}{value_pattern}{unit_separator}(?:mg/dl|mg/dL|μmol/L)?', re.IGNORECASE)
        patterns['albumina'] = re.compile(rf'(?:albumina|albumin|alb){separator_pattern}{value_pattern}{unit_separator}(?:g/dl|g/dL|g/l)?', re.IGNORECASE)
        patterns['proteinas_totais'] = re.compile(rf'(?:proteínas totais|proteinas totais|total proteins|pt|prot.*totais){separator_pattern}{value_pattern}{unit_separator}(?:g/dl|g/dL|g/l)?', re.IGNORECASE)
        patterns['globulinas'] = re.compile(rf'(?:globulinas|globulins|glob){separator_pattern}{value_pattern}{unit_separator}(?:g/dl|g/dL|g/l)?', re.IGNORECASE)

        # === COAGULAÇÃO ULTRA EXPANDIDA ===
        patterns['tp'] = re.compile(rf'(?:tp|tempo de protrombina|prothrombin time|tempo.*prot){separator_pattern}{value_pattern}{unit_separator}(?:seg|s|seconds|segundos)?', re.IGNORECASE)
        patterns['ttpa'] = re.compile(rf'(?:ttpa|tempo.*tromboplastina|aptt|aPTT|tempo.*parcial){separator_pattern}{value_pattern}{unit_separator}(?:seg|s|seconds|segundos)?', re.IGNORECASE)
        patterns['inr'] = re.compile(rf'(?:inr|international normalized ratio|i\.n\.r\.){separator_pattern}{value_pattern}', re.IGNORECASE)
        patterns['fibrinogenio'] = re.compile(rf'(?:fibrinogênio|fibrinogenio|fibrinogen|fibr){separator_pattern}{value_pattern}{unit_separator}(?:mg/dl|mg/dL)?', re.IGNORECASE)
        patterns['d_dimero'] = re.compile(rf'(?:d-dímero|d dimero|d-dimer|ddimer){separator_pattern}{value_pattern}{unit_separator}(?:ng/ml|ng/mL|μg/l)?', re.IGNORECASE)
        patterns['antitrombina_iii'] = re.compile(rf'(?:antitrombina iii|antithrombin iii|at3|at iii){separator_pattern}{value_pattern}{unit_separator}(?:%|percent)?', re.IGNORECASE)

        # === FUNÇÃO TIREOIDIANA ULTRA EXPANDIDA ===
        patterns['tsh'] = re.compile(rf'(?:tsh|hormônio tireoestimulante|thyroid stimulating hormone|t\.s\.h\.){separator_pattern}{value_pattern}{unit_separator}(?:mui/l|mUI/L|μiu/ml|uiu/ml)?', re.IGNORECASE)
        patterns['t4_livre'] = re.compile(rf'(?:t4 livre|t4l|free t4|tiroxina livre|ft4){separator_pattern}{value_pattern}{unit_separator}(?:ng/dl|ng/dL|pmol/L)?', re.IGNORECASE)
        patterns['t3_livre'] = re.compile(rf'(?:t3 livre|t3l|free t3|triiodotironina livre|ft3){separator_pattern}{value_pattern}{unit_separator}(?:pg/ml|pg/mL|pmol/L)?', re.IGNORECASE)
        patterns['t4_total'] = re.compile(rf'(?:t4 total|t4t|total t4|tiroxina total|tt4){separator_pattern}{value_pattern}{unit_separator}(?:μg/dl|ug/dl|nmol/L)?', re.IGNORECASE)
        patterns['t3_total'] = re.compile(rf'(?:t3 total|t3t|total t3|triiodotironina total|tt3){separator_pattern}{value_pattern}{unit_separator}(?:ng/dl|ng/dL|nmol/L)?', re.IGNORECASE)
        patterns['anti_tpo'] = re.compile(rf'(?:anti-tpo|anti tpo|anticorpos.*tpo|tpo.*antibodies){separator_pattern}{value_pattern}{unit_separator}(?:ui/ml|UI/mL|iu/ml)?', re.IGNORECASE)
        patterns['anti_tireoglobulina'] = re.compile(rf'(?:anti-tireoglobulina|anti tireoglobulina|anticorpos.*tireoglobulina|thyroglobulin.*antibodies){separator_pattern}{value_pattern}{unit_separator}(?:ui/ml|UI/mL|iu/ml)?', re.IGNORECASE)
        patterns['tireoglobulina'] = re.compile(rf'(?:tireoglobulina|thyroglobulin|tg){separator_pattern}{value_pattern}{unit_separator}(?:ng/ml|ng/mL)?', re.IGNORECASE)

        # === MARCADORES CARDÍACOS ULTRA EXPANDIDOS ===
        patterns['troponina_i'] = re.compile(rf'(?:troponina i|troponin i|tni|tn i){separator_pattern}{value_pattern}{unit_separator}(?:ng/ml|ng/mL|μg/l)?', re.IGNORECASE)
        patterns['troponina_t'] = re.compile(rf'(?:troponina t|troponin t|tnt|tn t){separator_pattern}{value_pattern}{unit_separator}(?:ng/ml|ng/mL|μg/l)?', re.IGNORECASE)
        patterns['troponina'] = re.compile(rf'(?:troponina|troponin|tn)(?!\s*[it]){separator_pattern}{value_pattern}{unit_separator}(?:ng/ml|ng/mL|μg/l)?', re.IGNORECASE)
        patterns['ck'] = re.compile(rf'(?:ck|creatina quinase|creatine kinase|cpk)(?!\s*mb){separator_pattern}{value_pattern}{unit_separator}(?:u/l|U/L|iu/l|ui/l)?', re.IGNORECASE)
        patterns['ck_mb'] = re.compile(rf'(?:ck-mb|ckmb|ck mb|cpk mb){separator_pattern}{value_pattern}{unit_separator}(?:ng/ml|ng/mL|u/l|U/L)?', re.IGNORECASE)
        patterns['ldh'] = re.compile(rf'(?:ldh|lactato desidrogenase|lactic dehydrogenase|dhl){separator_pattern}{value_pattern}{unit_separator}(?:u/l|U/L|iu/l|ui/l)?', re.IGNORECASE)
        patterns['mioglobina'] = re.compile(rf'(?:mioglobina|myoglobin|mio){separator_pattern}{value_pattern}{unit_separator}(?:ng/ml|ng/mL|μg/l)?', re.IGNORECASE)
        patterns['bnp'] = re.compile(rf'(?:bnp|brain natriuretic peptide|peptídeo natriurético){separator_pattern}{value_pattern}{unit_separator}(?:pg/ml|pg/mL)?', re.IGNORECASE)
        patterns['nt_probnp'] = re.compile(rf'(?:nt-probnp|nt probnp|n-terminal pro-bnp){separator_pattern}{value_pattern}{unit_separator}(?:pg/ml|pg/mL)?', re.IGNORECASE)

        # === MARCADORES INFLAMATÓRIOS ULTRA EXPANDIDOS ===
        patterns['pcr'] = re.compile(rf'(?:pcr|proteína c.*reativa|c.*reactive protein|crp){separator_pattern}{value_pattern}{unit_separator}(?:mg/l|mg/L|mg/dl)?', re.IGNORECASE)
        patterns['pcr_ultra_sensivel'] = re.compile(rf'(?:pcr.*ultra.*sensível|pcr.*ultra.*sensivel|high sensitivity crp|hs-crp){separator_pattern}{value_pattern}{unit_separator}(?:mg/l|mg/L)?', re.IGNORECASE)
        patterns['vhs'] = re.compile(rf'(?:vhs|velocidade.*hemossedimentação|esr|eritrossedimentação){separator_pattern}{value_pattern}{unit_separator}(?:mm/h|mm/1h)?', re.IGNORECASE)
        patterns['procalcitonina'] = re.compile(rf'(?:procalcitonina|procalcitonin|pct){separator_pattern}{value_pattern}{unit_separator}(?:ng/ml|ng/mL|μg/l)?', re.IGNORECASE)
        patterns['alfa_1_antitripsina'] = re.compile(rf'(?:alfa-1-antitripsina|alpha-1 antitrypsin|a1at){separator_pattern}{value_pattern}{unit_separator}(?:mg/dl|mg/dL)?', re.IGNORECASE)
        patterns['complemento_c3'] = re.compile(rf'(?:complemento c3|complement c3|c3){separator_pattern}{value_pattern}{unit_separator}(?:mg/dl|mg/dL)?', re.IGNORECASE)
        patterns['complemento_c4'] = re.compile(rf'(?:complemento c4|complement c4|c4){separator_pattern}{value_pattern}{unit_separator}(?:mg/dl|mg/dL)?', re.IGNORECASE)

        # === VITAMINAS E MINERAIS ULTRA EXPANDIDOS ===
        patterns['vitamina_b12'] = re.compile(rf'(?:vitamina b12|vitamin b12|b12|cobalamina){separator_pattern}{value_pattern}{unit_separator}(?:pg/ml|pg/mL|pmol/l)?', re.IGNORECASE)
        patterns['acido_folico'] = re.compile(rf'(?:ácido fólico|acido folico|folic acid|folate|folato){separator_pattern}{value_pattern}{unit_separator}(?:ng/ml|ng/mL|nmol/l)?', re.IGNORECASE)
        patterns['vitamina_d'] = re.compile(rf'(?:vitamina d|vitamin d|25.*oh.*vitamina d|25.*hidroxivitamina d|calcidiol){separator_pattern}{value_pattern}{unit_separator}(?:ng/ml|ng/mL|nmol/l)?', re.IGNORECASE)
        patterns['ferro'] = re.compile(rf'(?:ferro sérico|ferro serico|serum iron|ferro|fe){separator_pattern}{value_pattern}{unit_separator}(?:μg/dl|ug/dl|μmol/l)?', re.IGNORECASE)
        patterns['ferritina'] = re.compile(rf'(?:ferritina|ferritin|ferr){separator_pattern}{value_pattern}{unit_separator}(?:ng/ml|ng/mL|μg/l)?', re.IGNORECASE)
        patterns['transferrina'] = re.compile(rf'(?:transferrina|transferrin|transf){separator_pattern}{value_pattern}{unit_separator}(?:mg/dl|mg/dL|g/l)?', re.IGNORECASE)
        patterns['saturacao_transferrina'] = re.compile(rf'(?:saturação.*transferrina|saturacao.*transferrina|transferrin saturation){separator_pattern}{value_pattern}{unit_separator}(?:%|percent)?', re.IGNORECASE)

        # === PERFIL HORMONAL ULTRA EXPANDIDO ===
        patterns['cortisol'] = re.compile(rf'(?:cortisol|hydrocortisone|cort){separator_pattern}{value_pattern}{unit_separator}(?:μg/dl|ug/dl|nmol/l)?', re.IGNORECASE)
        patterns['insulina'] = re.compile(rf'(?:insulina|insulin|ins){separator_pattern}{value_pattern}{unit_separator}(?:μu/ml|uu/ml|pmol/l)?', re.IGNORECASE)
        patterns['testosterona_total'] = re.compile(rf'(?:testosterona total|total testosterone|testosterona){separator_pattern}{value_pattern}{unit_separator}(?:ng/dl|ng/dL|nmol/l)?', re.IGNORECASE)
        patterns['prolactina'] = re.compile(rf'(?:prolactina|prolactin|prl){separator_pattern}{value_pattern}{unit_separator}(?:ng/ml|ng/mL|μg/l)?', re.IGNORECASE)
        patterns['lh'] = re.compile(rf'(?:lh|hormônio luteinizante|luteinizing hormone){separator_pattern}{value_pattern}{unit_separator}(?:mui/ml|mUI/mL|iu/l)?', re.IGNORECASE)
        patterns['fsh'] = re.compile(rf'(?:fsh|hormônio folículo.*estimulante|follicle stimulating hormone){separator_pattern}{value_pattern}{unit_separator}(?:mui/ml|mUI/mL|iu/l)?', re.IGNORECASE)
        patterns['estradiol'] = re.compile(rf'(?:estradiol|17β.*estradiol|e2){separator_pattern}{value_pattern}{unit_separator}(?:pg/ml|pg/mL|pmol/l)?', re.IGNORECASE)
        patterns['progesterona'] = re.compile(rf'(?:progesterona|progesterone|prog){separator_pattern}{value_pattern}{unit_separator}(?:ng/ml|ng/mL|nmol/l)?', re.IGNORECASE)

        # === MARCADORES TUMORAIS ===
        patterns['psa_total'] = re.compile(rf'(?:psa total|total psa|antígeno prostático específico|psa){separator_pattern}{value_pattern}{unit_separator}(?:ng/ml|ng/mL)?', re.IGNORECASE)
        patterns['cea'] = re.compile(rf'(?:cea|antígeno carcinoembrionário|carcinoembryonic antigen){separator_pattern}{value_pattern}{unit_separator}(?:ng/ml|ng/mL)?', re.IGNORECASE)
        patterns['ca_125'] = re.compile(rf'(?:ca 125|ca-125|ca125|cancer antigen 125){separator_pattern}{value_pattern}{unit_separator}(?:u/ml|U/mL)?', re.IGNORECASE)
        patterns['ca_153'] = re.compile(rf'(?:ca 15-3|ca 153|ca15-3|cancer antigen 15-3){separator_pattern}{value_pattern}{unit_separator}(?:u/ml|U/mL)?', re.IGNORECASE)
        patterns['ca_199'] = re.compile(rf'(?:ca 19-9|ca 199|ca19-9|cancer antigen 19-9){separator_pattern}{value_pattern}{unit_separator}(?:u/ml|U/mL)?', re.IGNORECASE)
        patterns['alfa_fetoproteina'] = re.compile(rf'(?:alfa.*fetoproteína|alpha.*fetoprotein|afp){separator_pattern}{value_pattern}{unit_separator}(?:ng/ml|ng/mL)?', re.IGNORECASE)

        # === IMUNOLOGIA ===
        patterns['iga'] = re.compile(rf'(?:iga|imunoglobulina a|immunoglobulin a){separator_pattern}{value_pattern}{unit_separator}(?:mg/dl|mg/dL|g/l)?', re.IGNORECASE)
        patterns['igg'] = re.compile(rf'(?:igg|imunoglobulina g|immunoglobulin g){separator_pattern}{value_pattern}{unit_separator}(?:mg/dl|mg/dL|g/l)?', re.IGNORECASE)
        patterns['igm'] = re.compile(rf'(?:igm|imunoglobulina m|immunoglobulin m){separator_pattern}{value_pattern}{unit_separator}(?:mg/dl|mg/dL|g/l)?', re.IGNORECASE)
        patterns['ige'] = re.compile(rf'(?:ige|imunoglobulina e|immunoglobulin e){separator_pattern}{value_pattern}{unit_separator}(?:ui/ml|UI/mL|ku/l)?', re.IGNORECASE)

        # === FUNÇÃO PANCREÁTICA ===
        patterns['amilase'] = re.compile(rf'(?:amilase|amylase|amil){separator_pattern}{value_pattern}{unit_separator}(?:u/l|U/L|iu/l)?', re.IGNORECASE)
        patterns['lipase'] = re.compile(rf'(?:lipase|lip){separator_pattern}{value_pattern}{unit_separator}(?:u/l|U/L|iu/l)?', re.IGNORECASE)

        # === OUTROS EXAMES IMPORTANTES ===
        patterns['lactato'] = re.compile(rf'(?:lactato|lactic acid|ácido lático|lac){separator_pattern}{value_pattern}{unit_separator}(?:mmol/l|mg/dl)?', re.IGNORECASE)
        patterns['homocisteina'] = re.compile(rf'(?:homocisteína|homocysteine|hcys){separator_pattern}{value_pattern}{unit_separator}(?:μmol/l|umol/l)?', re.IGNORECASE)

        return patterns
    
    def extract_date_from_text(self, text: str) -> str:
        """Extract date from lab results text"""
        import re
        from datetime import datetime

        # Padrões para diferentes formatos de data
        date_patterns = [
            r'(?:data|realizado|coletado|exame)[:\s]*([0-3]?[0-9][\/\-\.][0-1]?[0-9][\/\-\.]20[0-9]{2})',
            r'([0-3]?[0-9][\/\-\.][0-1]?[0-9][\/\-\.]20[0-9]{2})',
            r'([0-3]?[0-9][\/\-\.][0-1]?[0-9][\/\-\.][0-9]{2})',
            r'(?:em|de)[:\s]*([0-3]?[0-9][\/\-\.][0-1]?[0-9][\/\-\.]20[0-9]{2})'
        ]

        for pattern in date_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            if matches:
                date_str = matches[0]
                # Padronizar formato da data
                date_str = date_str.replace('-', '/').replace('.', '/')
                try:
                    # Tentar parsear a data para validar
                    if len(date_str.split('/')[-1]) == 2:
                        # Adicionar 20 ao ano se for formato de 2 dígitos
                        parts = date_str.split('/')
                        date_str = f"{parts[0]}/{parts[1]}/20{parts[2]}"

                    # Validar se é uma data válida
                    datetime.strptime(date_str, '%d/%m/%Y')
                    return date_str
                except:
                    continue

        # Se não encontrar data, usar data atual
        return datetime.now().strftime('%d/%m/%Y')

    def parse_text(self, text: str) -> List[LabResult]:
        """Enhanced parser for lab results with better text processing"""
        results = []

        # Pre-process text to normalize formatting
        text = self._normalize_text(text)

        for test_name, pattern in self.patterns.items():
            matches = pattern.findall(text)
            if matches:
                for match in matches:
                    value_str = match.replace(',', '.').strip()
                    try:
                        # Handle different number formats
                        value_float = float(value_str)
                        ref = ReferenceRanges.get_reference(test_name)

                        if ref:
                            status = ReferenceRanges.check_status(test_name, value_float)
                            ref_range = f"{ref['min']}-{ref['max']} {ref['unit']}"

                            # Create formatted test name
                            formatted_test_name = self._format_test_name(test_name)

                            result = LabResult(
                                test_name=formatted_test_name,
                                value=value_str,
                                unit=ref['unit'],
                                reference_range=ref_range,
                                status=status,
                                category=ref['category']
                            )
                            results.append(result)
                    except ValueError:
                        continue

        # If no results found with patterns, try alternative parsing
        if not results:
            results = self._parse_alternative_format(text)

        return results

    def _normalize_text(self, text: str) -> str:
        """Ultra robust text normalization for better parsing"""
        # Remove extra whitespace and normalize line breaks
        text = re.sub(r'\s+', ' ', text)
        text = text.strip()

        # Normalize common separators and formatting
        text = re.sub(r'[:\-=]+\s*', ': ', text)
        text = re.sub(r'\s*[:\-=]\s*', ': ', text)

        # Normalize parentheses spacing
        text = re.sub(r'\s*\(\s*', ' (', text)
        text = re.sub(r'\s*\)\s*', ') ', text)

        # Normalize units - comprehensive list
        unit_normalizations = {
            # Common unit variations
            r'g\/dl': 'g/dL', r'g\/dL': 'g/dL', r'g\s*\/\s*dl': 'g/dL',
            r'mg\/dl': 'mg/dL', r'mg\/dL': 'mg/dL', r'mg\s*\/\s*dl': 'mg/dL',
            r'\/mm3': '/mm³', r'\/mm³': '/mm³', r'\s*\/\s*mm3': '/mm³',
            r'u\/l': 'U/L', r'U\/l': 'U/L', r'u\s*\/\s*l': 'U/L',
            r'ui\/l': 'UI/L', r'UI\/l': 'UI/L', r'ui\s*\/\s*l': 'UI/L',
            r'iu\/l': 'IU/L', r'IU\/l': 'IU/L', r'iu\s*\/\s*l': 'IU/L',
            r'meq\/l': 'mEq/L', r'mEq\/l': 'mEq/L', r'meq\s*\/\s*l': 'mEq/L',
            r'ng\/ml': 'ng/mL', r'ng\/mL': 'ng/mL', r'ng\s*\/\s*ml': 'ng/mL',
            r'pg\/ml': 'pg/mL', r'pg\/mL': 'pg/mL', r'pg\s*\/\s*ml': 'pg/mL',
            r'μg\/dl': 'μg/dL', r'ug\/dl': 'μg/dL', r'μg\s*\/\s*dl': 'μg/dL',
            r'μmol\/l': 'μmol/L', r'umol\/l': 'μmol/L', r'μmol\s*\/\s*l': 'μmol/L',
            r'nmol\/l': 'nmol/L', r'nmol\s*\/\s*l': 'nmol/L',
            r'pmol\/l': 'pmol/L', r'pmol\s*\/\s*l': 'pmol/L',
            r'mui\/l': 'mUI/L', r'mUI\/l': 'mUI/L', r'mui\s*\/\s*l': 'mUI/L',
            r'μiu\/ml': 'μIU/mL', r'uiu\/ml': 'μIU/mL', r'μiu\s*\/\s*ml': 'μIU/mL',

            # Scientific notation normalization
            r'x\s*10\s*\^?\s*([0-9]+)': r'x10^\1',
            r'×\s*10\s*\^?\s*([0-9]+)': r'x10^\1',
            r'X\s*10\s*\^?\s*([0-9]+)': r'x10^\1',

            # Common abbreviations
            r'\bmil\/mm3\b': 'x10³/mm³',
            r'\bmilhões\/mm3\b': 'x10⁶/mm³',
            r'\bmilhoes\/mm3\b': 'x10⁶/mm³',

            # Percentage normalization
            r'\bpercent\b': '%',
            r'\bpct\b': '%',

            # Time units
            r'\bseg\b': 's',
            r'\bsegundos\b': 's',
            r'\bseconds\b': 's',
            r'\bmin\b': 'min',
            r'\bminutos\b': 'min',
            r'\bminutes\b': 'min',
            r'\bh\b': 'h',
            r'\bhoras\b': 'h',
            r'\bhours\b': 'h',

            # Volume units
            r'\bml\b': 'mL',
            r'\bmilliliters\b': 'mL',
            r'\bmililitros\b': 'mL',
            r'\bl\b(?=\s|$)': 'L',  # Only match 'l' at word boundary
            r'\bliters\b': 'L',
            r'\blitros\b': 'L',

            # Weight units
            r'\bkg\b': 'kg',
            r'\bkilogramas\b': 'kg',
            r'\bkilograms\b': 'kg',
            r'\bg\b(?=\s|$)': 'g',  # Only match 'g' at word boundary
            r'\bgramas\b': 'g',
            r'\bgrams\b': 'g',
        }

        # Apply all unit normalizations
        for pattern, replacement in unit_normalizations.items():
            text = re.sub(pattern, replacement, text, flags=re.IGNORECASE)

        # Normalize decimal separators (convert comma to dot for numbers)
        text = re.sub(r'(\d+),(\d+)', r'\1.\2', text)

        # Clean up multiple spaces again after all transformations
        text = re.sub(r'\s+', ' ', text)

        # Remove leading/trailing whitespace
        text = text.strip()

        return text

    def _format_test_name(self, test_name: str) -> str:
        """Format test name for display"""
        name_mapping = {
            'hemoglobina': 'Hemoglobina',
            'hematocrito': 'Hematócrito',
            'eritrocitos': 'Eritrócitos',
            'leucocitos': 'Leucócitos',
            'plaquetas': 'Plaquetas',
            'vcm': 'VCM',
            'chcm': 'CHCM',
            'rdw': 'RDW',
            'glicose': 'Glicose',
            'creatinina': 'Creatinina',
            'ureia': 'Ureia',
            'acido_urico': 'Ácido Úrico',
            'colesterol_total': 'Colesterol Total',
            'hdl': 'HDL',
            'ldl': 'LDL',
            'triglicerides': 'Triglicerídeos',
            'alt_tgp': 'ALT (TGP)',
            'ast_tgo': 'AST (TGO)',
            'fosfatase_alcalina': 'Fosfatase Alcalina',
            'ggt': 'GGT',
            'bilirrubina_total': 'Bilirrubina Total',
            'bilirrubina_direta': 'Bilirrubina Direta',
            'sodio': 'Sódio',
            'potassio': 'Potássio',
            'calcio': 'Cálcio',
            'magnesio': 'Magnésio',
            'tsh': 'TSH',
            't4_livre': 'T4 Livre',
            't3_livre': 'T3 Livre',
            'tp': 'TP',
            'ttpa': 'TTPA',
            'inr': 'INR',
            'troponina': 'Troponina',
            'ck': 'CK',
            'ck_mb': 'CK-MB',
            'ldh': 'LDH',
            'pcr': 'PCR',
            'vhs': 'VHS'
        }

        return name_mapping.get(test_name, test_name.replace('_', ' ').title())

    def _parse_alternative_format(self, text: str) -> List[LabResult]:
        """Ultra robust alternative parsing for non-standard formats with intelligent exam detection"""
        results = []
        processed_matches = set()  # Avoid duplicates

        # Enhanced patterns for different text formats
        patterns_to_try = [
            # Pattern 1: Standard format with colon/equals
            re.compile(r'([a-záêçõ\s\-\(\)]+)[:\-=]\s*([0-9]+[.,]?[0-9]*(?:\s*[x×]\s*10\^?[0-9]+)?)\s*([a-z/%³²⁺⁻μ\s]*)', re.IGNORECASE),
            # Pattern 2: Exam name followed by value and unit
            re.compile(r'([a-záêçõ\s\-\(\)]+)\s+([0-9]+[.,]?[0-9]*(?:\s*[x×]\s*10\^?[0-9]+)?)\s*([a-z/%³²⁺⁻μ\s]*)', re.IGNORECASE),
            # Pattern 3: Value with unit followed by exam name (reverse order)
            re.compile(r'([0-9]+[.,]?[0-9]*(?:\s*[x×]\s*10\^?[0-9]+)?)\s*([a-z/%³²⁺⁻μ\s]*)\s+([a-záêçõ\s\-\(\)]+)', re.IGNORECASE),
            # Pattern 4: Parentheses format
            re.compile(r'([a-záêçõ\s\-]+)\s*\(\s*([0-9]+[.,]?[0-9]*(?:\s*[x×]\s*10\^?[0-9]+)?)\s*([a-z/%³²⁺⁻μ\s]*)\s*\)', re.IGNORECASE),
        ]

        for pattern in patterns_to_try:
            matches = pattern.findall(text)

            for match in matches:
                if len(match) == 3:
                    # Handle different match orders
                    if pattern == patterns_to_try[2]:  # Reverse order pattern
                        value_str, unit, test_name_raw = match
                    else:
                        test_name_raw, value_str, unit = match

                    # Clean and normalize
                    test_name_clean = self._normalize_test_name(test_name_raw.strip())
                    value_clean = value_str.replace(',', '.').strip()
                    unit_clean = unit.strip()

                    # Skip if already processed
                    match_key = f"{test_name_clean}_{value_clean}_{unit_clean}"
                    if match_key in processed_matches:
                        continue
                    processed_matches.add(match_key)

                    # Skip invalid values
                    if not value_clean or len(test_name_clean) < 2:
                        continue

                    # Try intelligent matching with known tests
                    matched_test = self._intelligent_test_matching(test_name_clean)

                    if matched_test:
                        try:
                            # Handle scientific notation
                            if 'x' in value_clean.lower() or '×' in value_clean:
                                value_clean = self._parse_scientific_notation(value_clean)

                            value_float = float(value_clean)
                            ref = ReferenceRanges.get_reference(matched_test)

                            if ref:
                                status = ReferenceRanges.check_status(matched_test, value_float)
                                ref_range = f"{ref['min']}-{ref['max']} {ref['unit']}"

                                result = LabResult(
                                    test_name=self._format_test_name(matched_test),
                                    value=value_clean,
                                    unit=unit_clean or ref['unit'],
                                    reference_range=ref_range,
                                    status=status,
                                    category=ref['category']
                                )
                                results.append(result)
                        except (ValueError, TypeError):
                            continue

        return results

    def _normalize_test_name(self, test_name: str) -> str:
        """Normalize test name for better matching"""
        # Remove common prefixes/suffixes and normalize
        test_name = test_name.lower().strip()

        # Remove common words that don't help identification
        remove_words = ['exame', 'teste', 'dosagem', 'nivel', 'nível', 'concentração', 'concentracao']
        for word in remove_words:
            test_name = test_name.replace(word, '').strip()

        # Normalize accents and special characters
        replacements = {
            'á': 'a', 'à': 'a', 'ã': 'a', 'â': 'a',
            'é': 'e', 'ê': 'e',
            'í': 'i', 'î': 'i',
            'ó': 'o', 'ô': 'o', 'õ': 'o',
            'ú': 'u', 'û': 'u',
            'ç': 'c'
        }

        for old, new in replacements.items():
            test_name = test_name.replace(old, new)

        return test_name

    def _intelligent_test_matching(self, test_name_clean: str) -> Optional[str]:
        """Intelligent matching using the comprehensive EXAMES_DICT"""
        # Direct match in EXAMES_DICT
        for full_name, abbrev in ReferenceRanges.EXAMES_DICT.items():
            if test_name_clean == full_name.lower():
                # Find the corresponding key in patterns
                for pattern_key in self.patterns.keys():
                    ref = ReferenceRanges.get_reference(pattern_key)
                    if ref and ref.get('abbrev') == abbrev:
                        return pattern_key

        # Partial matching with keywords
        for full_name, abbrev in ReferenceRanges.EXAMES_DICT.items():
            full_name_clean = full_name.lower()

            # Check if test name contains key words from the full name
            words_in_full_name = full_name_clean.split()
            words_in_test = test_name_clean.split()

            # Calculate match score
            matches = 0
            for word in words_in_test:
                if len(word) > 2:  # Only consider meaningful words
                    for full_word in words_in_full_name:
                        if word in full_word or full_word in word:
                            matches += 1
                            break

            # If good match, find corresponding pattern key
            if matches >= min(len(words_in_test), 2):
                for pattern_key in self.patterns.keys():
                    ref = ReferenceRanges.get_reference(pattern_key)
                    if ref and ref.get('abbrev') == abbrev:
                        return pattern_key

        # Fallback: try direct pattern key matching
        for pattern_key in self.patterns.keys():
            pattern_words = pattern_key.split('_')
            test_words = test_name_clean.split()

            matches = 0
            for test_word in test_words:
                if len(test_word) > 2:
                    for pattern_word in pattern_words:
                        if test_word in pattern_word or pattern_word in test_word:
                            matches += 1
                            break

            if matches >= min(len(test_words), len(pattern_words)):
                return pattern_key

        return None

    def _parse_scientific_notation(self, value_str: str) -> str:
        """Parse scientific notation like '4.5 x 10^6' or '4.5 × 10³'"""
        try:
            # Handle different formats
            value_str = value_str.replace('×', 'x').replace('^', '').replace('³', '3').replace('²', '2')

            if 'x' in value_str.lower():
                parts = value_str.lower().split('x')
                if len(parts) == 2:
                    base = float(parts[0].strip())
                    exp_part = parts[1].strip()

                    # Extract exponent
                    exp_match = re.search(r'10\s*(\d+)', exp_part)
                    if exp_match:
                        exponent = int(exp_match.group(1))
                        result = base * (10 ** exponent)
                        return str(result)

            return value_str
        except:
            return value_str

class AILabFormatter:
    """AI-powered lab results formatter using Claude or OpenAI"""

    def __init__(self, anthropic_api_key: str = None, openai_api_key: str = None, preferred_provider: str = None):
        # Anthropic setup
        self.anthropic_api_key = anthropic_api_key or os.getenv("ANTHROPIC_API_KEY")
        if self.anthropic_api_key:
            self.anthropic_client = Anthropic(api_key=self.anthropic_api_key)
        else:
            self.anthropic_client = None

        # OpenAI setup
        self.openai_api_key = openai_api_key or os.getenv("OPENAI_API_KEY")
        if self.openai_api_key:
            try:
                import openai
                # Check if we're using the new OpenAI client (>= 1.0.0) or the legacy one
                if hasattr(openai, 'OpenAI'):
                    self.openai_client = openai.OpenAI(api_key=self.openai_api_key)
                else:
                    # Legacy client
                    openai.api_key = self.openai_api_key
                    self.openai_client = openai
            except ImportError:
                st.warning("⚠️ OpenAI library not installed. Install with: pip install openai")
                self.openai_client = None
        else:
            self.openai_client = None

        # Set preferred provider
        self.preferred_provider = preferred_provider
        if not self.preferred_provider:
            if self.anthropic_client:
                self.preferred_provider = "claude"
            elif self.openai_client:
                self.preferred_provider = "openai"
            else:
                self.preferred_provider = None

        self.parser = LabTextParser()
    
    def extract_text_from_pdf(self, pdf_file) -> str:
        """Extract text from PDF file"""
        try:
            pdf_reader = PyPDF2.PdfReader(pdf_file)
            text = ""
            for page in pdf_reader.pages:
                text += page.extract_text() + "\n"
            return text
        except Exception as e:
            st.error(f"Erro ao extrair texto do PDF: {str(e)}")
            return ""
    
    def extract_text_from_image(self, image_file) -> str:
        """Extract text from image using OCR"""
        try:
            image = Image.open(image_file)
            text = pytesseract.image_to_string(image, lang='por')
            return text
        except Exception as e:
            st.error(f"Erro ao extrair texto da imagem: {str(e)}")
            return ""
    
    def get_ai_analysis(self, text: str, lab_results: List[LabResult]) -> Dict[str, str]:
        """Get AI analysis of lab results using Claude or OpenAI"""
        if not self.anthropic_client and not self.openai_client:
            return {
                "summary": "Análise de IA não disponível (nenhuma API key configurada)",
                "recommendations": "Configure uma API key (Anthropic ou OpenAI) para análises avançadas"
            }

        # Prepare context for AI
        results_context = "\n".join([
            f"- {result.test_name}: {result.value} {result.unit} (Status: {result.status}, Ref: {result.reference_range})"
            for result in lab_results
        ])

        prompt = f"""
        Analise os seguintes resultados de exames laboratoriais em português:

        RESULTADOS:
        {results_context}

        TEXTO ORIGINAL:
        {text[:1000]}...

        Por favor, forneça:
        1. Um resumo clínico conciso dos resultados
        2. Identificação de valores alterados e sua significância clínica
        3. Recomendações gerais (sempre mencionar que é necessário consultar um médico)

        Responda em português brasileiro, de forma profissional e clara.
        """

        # Determine which AI to use
        use_claude = (self.preferred_provider == "claude" or
                     (self.preferred_provider is None and self.anthropic_client))

        try:
            if use_claude and self.anthropic_client:
                # Use Claude (Anthropic)
                response = self.anthropic_client.messages.create(
                    model="claude-sonnet-4-20250514",
                    max_tokens=64000,
                    temperature=0.1,
                    messages=[{"role": "user", "content": prompt}]
                )
                ai_response = response.content[0].text
                provider_used = "Claude (Anthropic)"

            elif self.openai_client:
                # Use OpenAI GPT-4
                response = self.openai_client.chat.completions.create(
                    model="gpt-4o-mini",
                    messages=[{"role": "user", "content": prompt}],
                    max_tokens=64000,
                    temperature=0.1
                )
                ai_response = response.choices[0].message.content
                provider_used = "GPT-4 (OpenAI)"

            else:
                return {
                    "summary": "Erro: Nenhuma API disponível",
                    "recommendations": "Configure uma API key válida para análises de IA."
                }

            # Split response into summary and recommendations
            parts = ai_response.split("Recomendações")
            summary = parts[0].strip()
            recommendations = "Recomendações" + parts[1].strip() if len(parts) > 1 else "Consulte um médico para interpretação adequada."

            # Add provider info
            summary = f"**Análise por {provider_used}:**\n\n{summary}"

            return {
                "summary": summary,
                "recommendations": recommendations
            }

        except Exception as e:
            return {
                "summary": f"Erro na análise de IA: {str(e)}",
                "recommendations": "Consulte um médico para interpretação adequada dos resultados."
            }

    def format_results(self, lab_results: List[LabResult], exam_date: str = None) -> str:
        """Ultra Advanced Format with Standardized Categories and Slash-Separated Abbreviations"""
        if not lab_results:
            return "Nenhum resultado encontrado."

        # Use provided date or current date
        if not exam_date:
            from datetime import datetime
            exam_date = datetime.now().strftime('%d/%m/%Y')

        # Use SmartFormatter for ultra standardized formatting
        return SmartFormatter.format_for_medical_record(
            lab_results, 
            exam_date, 
            include_reference=False, 
            style="ultra_standardized"
        )

    def format_results_ultra_expanded(self, lab_results: List[LabResult], exam_date: str = None) -> str:
        """Ultra Expanded Format with All Categories and Detailed Slash Notation"""
        if not lab_results:
            return "Nenhum resultado encontrado."

        # Use provided date or current date
        if not exam_date:
            from datetime import datetime
            exam_date = datetime.now().strftime('%d/%m/%Y')

        # Group by category with ultra expanded mapping
        categories = {}
        for result in lab_results:
            category = result.category if result.category else 'outros'
            if category not in categories:
                categories[category] = []
            categories[category].append(result)

        formatted_sections = []

        # Ultra expanded category processing in standardized order
        category_order = SmartFormatter.CATEGORY_ORDER
        
        for category in category_order:
            if category not in categories:
                continue
                
            results = categories[category]
            category_title = SmartFormatter.CATEGORY_MAPPING.get(category, category.upper())
            
            # Process each category with ultra detailed formatting
            exam_parts = []
            for result in results:
                ref = ReferenceRanges.get_reference(result.test_name.lower().replace(' ', '_'))
                abbrev = ref.get('abbrev', result.test_name) if ref else result.test_name
                
                # Ultra detailed status indicators
                status_indicator = ""
                if result.status == "high":
                    status_indicator = "↑"
                elif result.status == "low":
                    status_indicator = "↓"
                elif result.status == "critical":
                    status_indicator = "⚠"
                
                # Clean value and unit
                value_clean = result.value.replace(',', '.')
                unit_clean = result.unit if result.unit else ''
                
                # Ultra standardized slash format: /ABBREV value unit status/
                if unit_clean and not any(u in value_clean for u in ['mg', 'g', 'mL', '%', 'U', 'mEq']):
                    exam_str = f"/{abbrev} {value_clean}{unit_clean}{status_indicator}/"
                else:
                    exam_str = f"/{abbrev} {value_clean}{status_indicator}/"
                
                exam_parts.append(exam_str)
            
            if exam_parts:
                # Ultra standardized format: CATEGORY: /exam1/ /exam2/ /exam3/
                section_line = f"{category_title}: {' '.join(exam_parts)}"
                formatted_sections.append(section_line)

        # Process remaining categories not in standard order
        remaining_categories = set(categories.keys()) - set(category_order)
        for category in remaining_categories:
            results = categories[category]
            exam_parts = []
            for result in results:
                ref = ReferenceRanges.get_reference(result.test_name.lower().replace(' ', '_'))
                abbrev = ref.get('abbrev', result.test_name) if ref else result.test_name
                
                status_indicator = ""
                if result.status == "high":
                    status_indicator = "↑"
                elif result.status == "low":
                    status_indicator = "↓"
                elif result.status == "critical":
                    status_indicator = "⚠"
                
                value_clean = result.value.replace(',', '.')
                unit_clean = result.unit if result.unit else ''
                
                if unit_clean and not any(u in value_clean for u in ['mg', 'g', 'mL', '%', 'U', 'mEq']):
                    exam_str = f"/{abbrev} {value_clean}{unit_clean}{status_indicator}/"
                else:
                    exam_str = f"/{abbrev} {value_clean}{status_indicator}/"
                
                exam_parts.append(exam_str)
            
            if exam_parts:
                category_title = category.upper().replace('_', ' ')
                section_line = f"{category_title}: {' '.join(exam_parts)}"
                formatted_sections.append(section_line)

        # Ultra compact final assembly
        if formatted_sections:
            all_results = " / ".join(formatted_sections)
            return f"Labs ({exam_date}): {all_results}"
        else:
            return f"Labs ({exam_date}): Nenhum resultado encontrado"

    def format_results_legacy_compact(self, lab_results: List[LabResult], exam_date: str = None) -> str:
        """Legacy ultra compact format - maintained for compatibility"""
        if not lab_results:
            return "Nenhum resultado encontrado."

        # Use provided date or current date
        if not exam_date:
            from datetime import datetime
            exam_date = datetime.now().strftime('%d/%m/%Y')

        # Group by category
        categories = {}
        for result in lab_results:
            if result.category not in categories:
                categories[result.category] = []
            categories[result.category].append(result)

        # Ultra expanded category mapping with standardized names
        category_names = {
            'hemograma': 'HEMOGRAMA',
            'bioquimica': 'BIOQUÍMICA', 
            'renal': 'FUNÇÃO RENAL',
            'eletrolitos': 'ELETRÓLITOS',
            'lipidico': 'PERFIL LIPÍDICO',
            'hepatico': 'FUNÇÃO HEPÁTICA',
            'coagulacao': 'COAGULAÇÃO',
            'tireoide': 'FUNÇÃO TIREOIDIANA',
            'hormonios': 'PERFIL HORMONAL',
            'cardiaco': 'MARCADORES CARDÍACOS',
            'inflamatorio': 'MARCADORES INFLAMATÓRIOS',
            'vitaminas': 'VITAMINAS E MINERAIS',
            'imunologia': 'IMUNOLOGIA',
            'pancreatico': 'FUNÇÃO PANCREÁTICA',
            'metabolico': 'PERFIL METABÓLICO',
            'oncologico': 'MARCADORES TUMORAIS',
            'reumatologico': 'PERFIL REUMATOLÓGICO',
            'neurologico': 'MARCADORES NEUROLÓGICOS',
            'endocrino': 'PERFIL ENDÓCRINO',
            'nutricional': 'ESTADO NUTRICIONAL'
        }

        formatted_parts = []

        # Ultra expanded processing for each category
        for category, results in categories.items():
            if not results:
                continue

            category_name = category_names.get(category, category.upper())
            parts = []

            for result in results:
                # Get ultra precise abbreviation from reference ranges
                ref = ReferenceRanges.get_reference(result.test_name.lower().replace(' ', '_'))
                abbrev = ref.get('abbrev', result.test_name[:4]) if ref else result.test_name[:4]

                # Ultra compact format with enhanced status indicators
                value_clean = result.value.replace(',', '.')
                unit_clean = result.unit.replace('/', '').replace(' ', '') if result.unit else ''

                # Status indicators for abnormal values
                status_symbol = ""
                if result.status == "high":
                    status_symbol = "↑"
                elif result.status == "low":
                    status_symbol = "↓"
                elif result.status == "critical":
                    status_symbol = "⚠"

                # Ultra standardized slash format
                if unit_clean and unit_clean.lower() not in value_clean.lower():
                    formatted_value = f"/{abbrev} {value_clean}{unit_clean}{status_symbol}/"
                else:
                    formatted_value = f"/{abbrev} {value_clean}{status_symbol}/"

                parts.append(formatted_value)

            if parts:
                # Ultra standardized category format
                category_line = f"{category_name}: {' '.join(parts)}"
                formatted_parts.append(category_line)

        # Ultra compact final assembly with enhanced structure
        if formatted_parts:
            all_results = " / ".join(formatted_parts)
            return f"Labs ({exam_date}): {all_results}"
        else:
            return f"Labs ({exam_date}): Nenhum resultado processado"

    def create_alerts(self, lab_results: List[LabResult]) -> List[Dict]:
        """Create alerts for abnormal values"""
        alerts = []

        for result in lab_results:
            if result.status in ['high', 'low']:
                alert = {
                    'test': result.test_name,
                    'value': f"{result.value} {result.unit}",
                    'status': result.status,
                    'reference': result.reference_range,
                    'severity': 'warning' if result.status in ['high', 'low'] else 'critical'
                }
                alerts.append(alert)

        return alerts

    def process_input(self, text: str = None, file_data: Any = None, file_type: str = None) -> ProcessedResults:
        """Process input text or file and return formatted results"""

        # Extract text from different sources
        if file_data and file_type:
            if file_type == 'pdf':
                text = self.extract_text_from_pdf(file_data)
            elif file_type in ['png', 'jpg', 'jpeg']:
                text = self.extract_text_from_image(file_data)

        if not text:
            return ProcessedResults(
                formatted_text="Nenhum texto encontrado para processar.",
                alerts=[],
                raw_data=[],
                summary="Erro: Texto vazio",
                recommendations="Verifique o arquivo ou texto fornecido."
            )

        # Extract date from text
        exam_date = self.parser.extract_date_from_text(text)

        # Parse lab results
        lab_results = self.parser.parse_text(text)

        # Format results with date
        formatted_text = self.format_results(lab_results, exam_date)

        # Create alerts
        alerts = self.create_alerts(lab_results)

        # Get AI analysis
        ai_analysis = self.get_ai_analysis(text, lab_results)

        return ProcessedResults(
            formatted_text=formatted_text,
            alerts=alerts,
            raw_data=lab_results,
            summary=ai_analysis['summary'],
            recommendations=ai_analysis['recommendations']
        )

def main():
    """LabsForm Pro Ultra - Advanced AI-Powered Lab Results Formatter"""

    # Page configuration
    st.set_page_config(
        page_title="LabsForm Pro - Medical Laboratory Results Formatter",
        page_icon="⚕️",
        layout="wide",
        initial_sidebar_state="expanded"
    )

    # Ultra Modern Architecture CSS with Red Tones
    st.markdown("""
    <style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

    /* Global Architecture */
    .main {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%);
    }

    /* Ultra Modern Header */
    .main-header {
        background: linear-gradient(135deg, #dc2626 0%, #b91c1c 50%, #991b1b 100%);
        padding: 4rem 3rem;
        border-radius: 16px;
        color: white;
        text-align: center;
        margin-bottom: 3rem;
        box-shadow: 0 20px 40px rgba(220, 38, 38, 0.2);
        position: relative;
        overflow: hidden;
    }

    .main-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        opacity: 0.3;
    }

    .main-header h1 {
        font-size: 3.5rem;
        font-weight: 800;
        margin-bottom: 1rem;
        letter-spacing: -0.05em;
        position: relative;
        z-index: 1;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .main-header h3 {
        font-size: 1.5rem;
        font-weight: 400;
        opacity: 0.95;
        margin-bottom: 1.5rem;
        position: relative;
        z-index: 1;
    }

    .main-header p {
        font-size: 1.1rem;
        opacity: 0.85;
        font-weight: 300;
        position: relative;
        z-index: 1;
        max-width: 600px;
        margin: 0 auto;
        line-height: 1.6;
    }

    /* Ultra Modern Cards */
    .metric-card {
        background: linear-gradient(135deg, #ffffff 0%, #fafafa 100%);
        padding: 2rem;
        border-radius: 12px;
        border: 1px solid #e5e7eb;
        text-align: center;
        margin: 1rem 0;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
    }

    .metric-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #dc2626, #ef4444, #f87171);
    }

    .metric-card:hover {
        box-shadow: 0 12px 40px rgba(220, 38, 38, 0.15);
        transform: translateY(-4px);
        border-color: #fecaca;
    }

    /* Advanced Alert System */
    .alert-box {
        padding: 1.5rem;
        border-radius: 12px;
        margin: 1rem 0;
        border-left: 6px solid;
        font-weight: 500;
        backdrop-filter: blur(10px);
        position: relative;
    }

    .alert-high {
        background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
        border-left-color: #dc2626;
        color: #991b1b;
        box-shadow: 0 4px 20px rgba(220, 38, 38, 0.1);
    }

    .alert-low {
        background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
        border-left-color: #2563eb;
        color: #1d4ed8;
        box-shadow: 0 4px 20px rgba(37, 99, 235, 0.1);
    }

    .success-box {
        background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
        border-left: 6px solid #16a34a;
        color: #166534;
        padding: 1.5rem;
        border-radius: 12px;
        margin: 1rem 0;
        font-weight: 500;
        box-shadow: 0 4px 20px rgba(22, 163, 74, 0.1);
    }

    /* Ultra Modern Score Card */
    .score-card {
        background: linear-gradient(135deg, #dc2626 0%, #b91c1c 50%, #991b1b 100%);
        padding: 3rem 2rem;
        border-radius: 16px;
        color: white;
        text-align: center;
        margin: 2rem 0;
        box-shadow: 0 20px 40px rgba(220, 38, 38, 0.2);
        position: relative;
        overflow: hidden;
    }

    .score-card::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        animation: pulse 4s ease-in-out infinite;
    }

    @keyframes pulse {
        0%, 100% { transform: scale(1); opacity: 0.5; }
        50% { transform: scale(1.1); opacity: 0.8; }
    }

    /* Architectural Feature Cards */
    .feature-card {
        background: linear-gradient(135deg, #ffffff 0%, #fafafa 100%);
        padding: 2rem;
        border-radius: 16px;
        border: 1px solid #e5e7eb;
        margin: 1.5rem 0;
        box-shadow: 0 8px 30px rgba(0,0,0,0.06);
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
    }

    .feature-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 2px;
        background: linear-gradient(90deg, #dc2626, #ef4444, #f87171);
        transform: scaleX(0);
        transition: transform 0.3s ease;
    }

    .feature-card:hover::before {
        transform: scaleX(1);
    }

    .feature-card:hover {
        box-shadow: 0 20px 60px rgba(220, 38, 38, 0.12);
        transform: translateY(-8px);
        border-color: #fecaca;
    }

    /* Ultra Modern Tab System */
    .stTabs [data-baseweb="tab-list"] {
        gap: 8px;
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        padding: 8px;
        border-radius: 16px;
        box-shadow: inset 0 2px 4px rgba(0,0,0,0.06);
    }

    .stTabs [data-baseweb="tab"] {
        height: 56px;
        padding: 0 32px;
        background-color: transparent;
        border-radius: 12px;
        border: none;
        font-weight: 600;
        color: #64748b;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
    }

    .stTabs [data-baseweb="tab"]::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, #dc2626, #ef4444);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .stTabs [aria-selected="true"] {
        background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
        color: white;
        box-shadow: 0 8px 25px rgba(220, 38, 38, 0.3);
        transform: translateY(-2px);
    }

    .stTabs [aria-selected="true"]::before {
        opacity: 1;
    }

    /* Ultra Modern Button System */
    .stButton > button {
        border-radius: 12px;
        font-weight: 600;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        border: none;
        background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
        color: white;
        padding: 0.75rem 2rem;
        box-shadow: 0 4px 15px rgba(220, 38, 38, 0.3);
    }

    .stButton > button:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(220, 38, 38, 0.4);
        background: linear-gradient(135deg, #b91c1c 0%, #dc2626 100%);
    }

    /* Architectural Sidebar */
    .css-1d391kg {
        background: linear-gradient(180deg, #fafafa 0%, #f5f5f5 100%);
        border-right: 1px solid #e5e7eb;
    }

    /* Ultra Modern Input Fields */
    .stTextInput > div > div > input {
        border-radius: 12px;
        border: 2px solid #e5e7eb;
        transition: all 0.3s ease;
        font-weight: 500;
    }

    .stTextInput > div > div > input:focus {
        border-color: #dc2626;
        box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
    }

    .stTextArea > div > div > textarea {
        border-radius: 12px;
        border: 2px solid #e5e7eb;
        transition: all 0.3s ease;
        font-weight: 500;
    }

    .stTextArea > div > div > textarea:focus {
        border-color: #dc2626;
        box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
    }

    /* Architectural Layout */
    .block-container {
        padding-top: 3rem;
        max-width: 1400px;
    }

    /* Ultra Modern Selectbox */
    .stSelectbox > div > div {
        border-radius: 12px;
        border: 2px solid #e5e7eb;
        transition: all 0.3s ease;
    }

    .stSelectbox > div > div:focus-within {
        border-color: #dc2626;
        box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
    }

    /* Modern Checkbox */
    .stCheckbox > label {
        font-weight: 500;
        color: #374151;
    }

    /* Ultra Modern Metrics */
    [data-testid="metric-container"] {
        background: linear-gradient(135deg, #ffffff 0%, #fafafa 100%);
        border: 1px solid #e5e7eb;
        padding: 1.5rem;
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
    }

    [data-testid="metric-container"]:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 30px rgba(220, 38, 38, 0.1);
        border-color: #fecaca;
    }

    /* Modern Divider */
    hr {
        border: none;
        height: 2px;
        background: linear-gradient(90deg, transparent, #dc2626, transparent);
        margin: 2rem 0;
    }

    /* Clinical Notes Styling */
    .clinical-note-card {
        background: linear-gradient(135deg, #ffffff 0%, #fafafa 100%);
        border: 1px solid #e5e7eb;
        border-left: 4px solid #dc2626;
        border-radius: 8px;
        padding: 1rem;
        margin: 0.5rem 0;
        box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        transition: all 0.3s ease;
    }

    .clinical-note-card:hover {
        box-shadow: 0 4px 16px rgba(220, 38, 38, 0.1);
        transform: translateY(-1px);
    }

    .note-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.5rem;
    }

    .note-title {
        margin: 0;
        color: #dc2626;
        font-weight: 600;
        font-size: 0.9rem;
        font-family: 'Inter', sans-serif;
    }

    .note-date {
        color: #6b7280;
        font-size: 0.75rem;
        font-family: 'Inter', sans-serif;
    }

    .note-content-preview {
        margin: 0;
        color: #374151;
        font-size: 0.85rem;
        line-height: 1.4;
        font-family: 'Inter', sans-serif;
    }

    .note-full-content {
        background: #f8fafc;
        border: 1px solid #e2e8f0;
        border-radius: 6px;
        padding: 1rem;
        margin: 0.5rem 0;
        font-family: 'Inter', sans-serif;
    }

    .note-full-title {
        color: #dc2626;
        margin-bottom: 0.5rem;
        font-weight: 600;
    }

    .note-full-text {
        color: #374151;
        margin: 0;
        white-space: pre-wrap;
        line-height: 1.5;
    }

    /* Sidebar Expander Styling */
    .streamlit-expanderHeader {
        background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
        color: white;
        border-radius: 8px;
        font-weight: 600;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .main-header h1 {
            font-size: 2.5rem;
        }

        .main-header {
            padding: 2rem 1.5rem;
        }

        .metric-card, .feature-card {
            padding: 1.5rem;
        }

        .clinical-note-card {
            padding: 0.75rem;
        }

        .note-title {
            font-size: 0.8rem;
        }

        .note-content-preview {
            font-size: 0.8rem;
        }
    }
    </style>
    """, unsafe_allow_html=True)

    # Ultra Modern Architectural Header
    st.markdown("""
    <div class="main-header">
        <h1>LabsForm Pro</h1>
        <h3>Enterprise Medical Laboratory Platform</h3>
        <p>Advanced AI-Powered Analysis • Intelligent Data Processing • Professional Reporting • Clinical Decision Support</p>
    </div>
    """, unsafe_allow_html=True)

    # Ultra Modern Architectural Sidebar
    with st.sidebar:
        st.markdown("""
        <div style="text-align: center; padding: 1rem 0; margin-bottom: 2rem;">
            <h2 style="color: #dc2626; font-weight: 800; margin: 0;">System Configuration</h2>
            <p style="color: #6b7280; font-size: 0.9rem; margin: 0.5rem 0 0 0;">Enterprise Settings Panel</p>
        </div>
        """, unsafe_allow_html=True)

        # AI Integration Section
        st.markdown("### AI Integration Layer")

        # Anthropic API Key
        api_key = st.text_input(
            "Anthropic Claude API",
            type="password",
            help="Enterprise AI analysis with Claude Sonnet 4",
            placeholder="sk-ant-api03-..."
        )

        # OpenAI API Key
        openai_api_key = st.text_input(
            "OpenAI GPT-4 API",
            type="password",
            help="Alternative AI analysis with GPT-4 Turbo",
            placeholder="sk-..."
        )

        # API selection
        if api_key and openai_api_key:
            ai_provider = st.selectbox(
                "Primary AI Provider",
                ["Claude (Anthropic)", "GPT-4 (OpenAI)"],
                help="Select primary AI engine for analysis"
            )
        elif api_key:
            ai_provider = "Claude (Anthropic)"
            st.success("Claude AI Engine Active")
        elif openai_api_key:
            ai_provider = "GPT-4 (OpenAI)"
            st.success("OpenAI Engine Active")
        else:
            ai_provider = None
            st.error("No AI Engine Configured")

        st.divider()

        # Processing Configuration
        st.markdown("### Processing Configuration")

        show_historical = st.checkbox('Historical Data Extraction', value=True, help="Advanced temporal data analysis")
        use_ai_analysis = st.checkbox('AI Clinical Analysis', value=True, help="Intelligent result interpretation")

        # Advanced features
        st.checkbox('Predictive Analytics', value=True, help="Machine learning predictions (Enterprise)", disabled=True)
        st.checkbox('Risk Stratification', value=True, help="Clinical risk assessment (Enterprise)", disabled=True)

        st.divider()

        # Output Configuration
        st.markdown("### Output Configuration")
        format_style = st.selectbox(
            "Output Format",
            ["compact", "detailed"],
            help="Compact: Enterprise format / Detailed: Clinical format"
        )

        include_reference = st.checkbox('Reference Ranges', value=False, help="Include clinical reference values")

        st.divider()

        # Clinical Notes System
        notes_count = len(st.session_state.get('clinical_notes', []))
        st.markdown(f"### Clinical Notes System ({notes_count})")

        # Initialize notes in session state if not exists
        if 'clinical_notes' not in st.session_state:
            st.session_state.clinical_notes = []

        # Note creation interface
        with st.expander("Create New Note", expanded=False):
            # Template selection
            template_options = {
                "Blank Note": {"title": "", "content": ""},
                "Patient Analysis": {
                    "title": "Patient Analysis - {date}",
                    "content": """Patient ID:
Date of Analysis: {date}

Clinical Findings:
-

Laboratory Results Summary:
-

Clinical Interpretation:
-

Recommendations:
-

Follow-up Required:
-

Notes:
- """
                },
                "Lab Interpretation": {
                    "title": "Lab Interpretation - {date}",
                    "content": """Laboratory Test Interpretation

Test Results:
-

Reference Values:
-

Clinical Significance:
-

Abnormal Findings:
-

Clinical Correlation:
-

Recommendations:
- """
                },
                "Treatment Plan": {
                    "title": "Treatment Plan - {date}",
                    "content": """Treatment Plan

Current Condition:
-

Laboratory Indicators:
-

Treatment Objectives:
-

Proposed Interventions:
-

Monitoring Parameters:
-

Expected Outcomes:
-

Review Schedule:
- """
                }
            }

            selected_template = st.selectbox(
                "Note Template",
                list(template_options.keys()),
                help="Choose a template to start with"
            )

            # Apply template
            template = template_options[selected_template]
            from datetime import datetime
            current_date = datetime.now().strftime('%d/%m/%Y')

            # Check if we need to clear inputs
            if st.session_state.get('clear_note_inputs', False):
                default_title = ""
                default_content = ""
                st.session_state.clear_note_inputs = False
            else:
                default_title = template["title"].format(date=current_date) if template["title"] else ""
                default_content = template["content"].format(date=current_date) if template["content"] else ""

            note_title = st.text_input(
                "Note Title",
                value=default_title,
                placeholder="e.g., Patient Analysis, Lab Interpretation...",
                key="note_title_input"
            )

            note_content = st.text_area(
                "Note Content",
                value=default_content,
                placeholder="Enter your clinical notes, observations, or analysis...",
                height=200,
                key="note_content_input"
            )

            col_save, col_clear = st.columns(2)
            with col_save:
                if st.button("Save Note", type="primary", use_container_width=True):
                    if note_title and note_content:
                        from datetime import datetime
                        new_note = {
                            'id': len(st.session_state.clinical_notes) + 1,
                            'title': note_title,
                            'content': note_content,
                            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M'),
                            'date_created': datetime.now().strftime('%d/%m/%Y')
                        }
                        st.session_state.clinical_notes.append(new_note)
                        st.success("Note saved successfully")
                        # Clear inputs by setting clear flag
                        st.session_state.clear_note_inputs = True
                        st.rerun()
                    else:
                        st.warning("Please enter both title and content")

            with col_clear:
                if st.button("Clear", use_container_width=True):
                    st.session_state.clear_note_inputs = True
                    st.rerun()

        # Quick access to notes
        if st.session_state.clinical_notes:
            st.markdown(f"**{len(st.session_state.clinical_notes)} notes saved**")
            st.info("📋 Access all notes in the 'Clinical Notes' tab below")
        else:
            st.info("No clinical notes saved yet")

        st.divider()

        # Enterprise Instructions
        st.markdown("### System Operations")
        st.markdown("""
        **Enterprise Workflow:**

        **Data Input Layer**
        • Paste laboratory results
        • Upload PDF documents
        • Import image files

        **Processing Engine**
        • Configure AI parameters
        • Set output preferences
        • Execute analysis pipeline

        **Output Generation**
        • Review formatted results
        • Validate clinical data
        • Export professional reports

        **Enterprise Features:**
        • Advanced AI clinical analysis
        • Standardized medical formatting
        • Interactive data visualizations
        • Comprehensive educational platform
        • Professional reporting system

        **Clinical Compliance:**
        • Always validate results with healthcare professionals
        • This system provides analytical support only
        • Maintain clinical oversight at all times
        """)

    # Initialize advanced formatter with both APIs
    preferred_provider = None
    if 'ai_provider' in locals():
        if ai_provider == "🤖 Claude (Anthropic)":
            preferred_provider = "claude"
        elif ai_provider == "🧠 GPT-4 (OpenAI)":
            preferred_provider = "openai"

    formatter = AILabFormatter(
        anthropic_api_key=api_key,
        openai_api_key=openai_api_key if 'openai_api_key' in locals() else None,
        preferred_provider=preferred_provider
    )

    # Ultra Modern Enterprise Interface
    tab1, tab2, tab3, tab4, tab5 = st.tabs(["Data Processing", "Advanced Analytics", "Clinical Education", "Enterprise Support", "Clinical Notes"])

    with tab1:
        # Ultra Modern Processing Header
        st.markdown("""
        <div class="feature-card">
            <h2 style="color: #dc2626; font-weight: 700; margin-bottom: 0.5rem;">Enterprise Data Processing Engine</h2>
            <p style="color: #6b7280; margin: 0;">Advanced laboratory data analysis and standardization platform</p>
        </div>
        """, unsafe_allow_html=True)

        # Input section
        col1, col2 = st.columns([1, 1])

        with col1:
            st.markdown("### Data Input Layer")

            # Input method selection
            input_method = st.radio(
                "Select input method:",
                ["Text Input", "File Upload"],
                horizontal=True
            )

            text_input = None
            file_data = None
            file_type = None

            if input_method == "Text Input":
                text_input = st.text_area(
                    "Enter laboratory results:",
                    height=350,
                    placeholder="""Example with standardized formatting:
Date: 03/15/2024

COMPLETE BLOOD COUNT
Hemoglobin: 14.2 g/dL
Hematocrit: 42%
Red Blood Cells: 4.5 million/mm³
White Blood Cells: 7,500/mm³
Platelets: 280,000/mm³

SERUM BIOCHEMISTRY
Glucose: 92 mg/dL
Creatinine: 0.9 mg/dL
Urea: 28 mg/dL
Total Cholesterol: 185 mg/dL

LIVER FUNCTION
ALT (TGP): 25 U/L
AST (TGO): 22 U/L
Total Bilirubin: 0.8 mg/dL

THYROID FUNCTION
TSH: 2.1 mUI/L
Free T4: 1.2 ng/dL

Expected output:
Labs (03/15/2024): Hemograma: /Hb 14.2g/dL/ /Ht 42%/ /Hem 4.5milhões/mm³/ /Leuco 7500/mm³/ /Plaq 280000/mm³/ Bioquímica: /Gli 92mg/dL/ /Cr 0.9mg/dL/ /Ur 28mg/dL/ /CT 185mg/dL/ Hepático: /TGP 25U/L/ /TGO 22U/L/ /BT 0.8mg/dL/ Tireoide: /TSH 2.1mUI/L/ /T4L 1.2ng/dL/"""
                )

            else:
                uploaded_file = st.file_uploader(
                    "Choose a file:",
                    type=['pdf', 'png', 'jpg', 'jpeg'],
                    help="Support for PDF and images (PNG, JPG) - maximum 10MB"
                )

                if uploaded_file:
                    file_data = uploaded_file
                    file_type = uploaded_file.type.split('/')[-1]
                    if file_type == 'jpeg':
                        file_type = 'jpg'

                    st.success(f"✅ Arquivo carregado: {uploaded_file.name} ({uploaded_file.size/1024/1024:.1f} MB)")

            # Processing Configuration
            st.markdown("### Processing Configuration Layer")
            col_a, col_b = st.columns(2)
            with col_a:
                extract_historical = st.checkbox("Historical Data Extraction", value=show_historical)
            with col_b:
                # Enterprise formatting is always enabled
                st.checkbox("Enterprise Formatting", value=True, disabled=True, help="Always active")

            # Enterprise Process button
            if st.button("Execute Data Processing Pipeline", type="primary", use_container_width=True):
                if text_input or file_data:
                    with st.spinner("Processing laboratory data with enterprise AI engine..."):
                        try:
                            # Process with advanced formatter
                            results = formatter.process_input(
                                text=text_input,
                                file_data=file_data,
                                file_type=file_type
                            )

                            # Extract historical data if requested
                            historical_data = {}
                            df_historical = pd.DataFrame()

                            if extract_historical and text_input:
                                historical_data = AdvancedAnalytics.extract_historical_data(text_input)
                                df_historical = AdvancedAnalytics.create_trends_dataframe(historical_data)

                            # Calculate lab score
                            lab_score = AdvancedAnalytics.calculate_lab_score(results.raw_data)

                            # Store all results in session state
                            st.session_state['results'] = results
                            st.session_state['historical_data'] = historical_data
                            st.session_state['df_historical'] = df_historical
                            st.session_state['lab_score'] = lab_score
                            st.session_state['original_text'] = text_input or ""

                            st.success("Enterprise processing pipeline completed successfully")

                            # Auto-create note option
                            if st.checkbox("Auto-create clinical note from results", value=False):
                                if 'clinical_notes' not in st.session_state:
                                    st.session_state.clinical_notes = []

                                # Generate automatic note
                                from datetime import datetime
                                auto_note = {
                                    'id': len(st.session_state.clinical_notes) + 1,
                                    'title': f"Lab Analysis - {datetime.now().strftime('%d/%m/%Y %H:%M')}",
                                    'content': f"""Automated Clinical Note

Formatted Results:
{results.formatted_text}

AI Summary:
{results.summary}

Clinical Recommendations:
{results.recommendations}

Raw Data Count: {len(results.raw_data)} tests processed
Alerts Generated: {len(results.alerts)} alerts

Processing Date: {datetime.now().strftime('%d/%m/%Y %H:%M')}
""",
                                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M'),
                                    'date_created': datetime.now().strftime('%d/%m/%Y')
                                }

                                st.session_state.clinical_notes.append(auto_note)
                                st.info("Clinical note automatically created and saved")

                        except Exception as e:
                            st.error(f"Processing pipeline error: {str(e)}")
                            import traceback
                            st.error(f"Technical details: {traceback.format_exc()}")
                else:
                    st.warning("Please provide laboratory data or upload a file to process")

        with col2:
            st.markdown("### Enterprise Results Dashboard")

            if 'results' in st.session_state:
                results = st.session_state['results']
                lab_score = st.session_state.get('lab_score', {})

                # Lab Health Score Card
                if lab_score:
                    st.markdown(f"""
                    <div class="score-card">
                        <h2>🏆 Score de Saúde Laboratorial</h2>
                        <h1 style="font-size: 3rem; margin: 0;">{lab_score['score']}%</h1>
                        <h3>{lab_score['grade']}</h3>
                        <p>{lab_score['summary']}</p>
                    </div>
                    """, unsafe_allow_html=True)

                # Advanced Formatted Results
                st.markdown("### 📋 Resultado Formatado Ultra")

                # Format with advanced options
                if results.raw_data:
                    # Extract date for formatting
                    exam_date = formatter.parser.extract_date_from_text(
                        st.session_state.get('original_text', '')
                    ) if 'original_text' in st.session_state else datetime.now().strftime('%d/%m/%Y')

                    # Use SmartFormatter for ultra standardized formatting
                    smart_formatted = SmartFormatter.format_for_medical_record(
                        results.raw_data,
                        exam_date,
                        include_reference=include_reference,
                        style="ultra_standardized"
                    )

                    st.text_area(
                        "Resultado formatado com IA:",
                        value=smart_formatted,
                        height=250,
                        key="smart_formatted_output"
                    )

                    # Action buttons
                    col_btn1, col_btn2, col_btn3 = st.columns(3)
                    with col_btn1:
                        if st.button("📋 Copiar", use_container_width=True):
                            st.code(smart_formatted, language=None)
                            st.success("✅ Copiado!")

                    with col_btn2:
                        # Download as JSON
                        download_data = {
                            "formatted_result": smart_formatted,
                            "lab_score": lab_score,
                            "alerts": results.alerts,
                            "ai_analysis": {
                                "summary": results.summary,
                                "recommendations": results.recommendations
                            },
                            "timestamp": datetime.now().isoformat()
                        }

                        st.download_button(
                            label="💾 Download",
                            data=json.dumps(download_data, indent=2, ensure_ascii=False),
                            file_name=f"exames_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                            mime="application/json",
                            use_container_width=True
                        )

                    with col_btn3:
                        if st.button("🔄 Reprocessar", use_container_width=True):
                            st.rerun()

                # Enhanced Alerts Section
                if results.alerts:
                    st.markdown("### 🚨 Valores Alterados")

                    # Group alerts by severity
                    high_alerts = [a for a in results.alerts if a['status'] == 'high']
                    low_alerts = [a for a in results.alerts if a['status'] == 'low']

                    if high_alerts:
                        st.markdown("#### 🔴 Valores Elevados")
                        for alert in high_alerts:
                            st.markdown(f"""
                            <div class="alert-box alert-high">
                                <strong>🔺 {alert['test']}:</strong> {alert['value']} - ALTO<br>
                                <small>📊 Referência: {alert['reference']}</small>
                            </div>
                            """, unsafe_allow_html=True)

                    if low_alerts:
                        st.markdown("#### 🔵 Valores Diminuídos")
                        for alert in low_alerts:
                            st.markdown(f"""
                            <div class="alert-box alert-low">
                                <strong>🔻 {alert['test']}:</strong> {alert['value']} - BAIXO<br>
                                <small>📊 Referência: {alert['reference']}</small>
                            </div>
                            """, unsafe_allow_html=True)

                # Enhanced AI Analysis
                if use_ai_analysis and api_key and results.summary:
                    st.markdown("### 🤖 Análise Clínica Avançada")

                    # AI Analysis tabs
                    ai_tab1, ai_tab2, ai_tab3 = st.tabs(["📊 Resumo", "💡 Recomendações", "🎯 Insights"])

                    with ai_tab1:
                        st.markdown("#### 📋 Resumo Clínico")
                        st.markdown(results.summary)

                    with ai_tab2:
                        st.markdown("#### 💊 Recomendações Médicas")
                        st.markdown(results.recommendations)

                    with ai_tab3:
                        st.markdown("#### 🎯 Insights Inteligentes")
                        if results.alerts:
                            st.write("**Principais achados:**")
                            for alert in results.alerts[:3]:  # Top 3 alerts
                                st.write(f"• {alert['test']}: {alert['value']} ({alert['status'].upper()})")

                        if lab_score:
                            st.write(f"**Score geral:** {lab_score['score']}% - {lab_score['grade']}")

                # Enhanced Data Table
                if results.raw_data:
                    st.markdown("### 📋 Dados Detalhados Ultra")

                    df_data = []
                    for result in results.raw_data:
                        # Add status emoji
                        status_emoji = "✅" if result.status == "normal" else ("🔴" if result.status == "high" else "🔵")

                        df_data.append({
                            'Status': status_emoji,
                            'Exame': result.test_name,
                            'Valor': result.value,
                            'Unidade': result.unit,
                            'Referência': result.reference_range,
                            'Categoria': result.category.title()
                        })

                    df = pd.DataFrame(df_data)
                    st.dataframe(
                        df,
                        use_container_width=True,
                        column_config={
                            "Status": st.column_config.TextColumn("Status", width="small"),
                            "Exame": st.column_config.TextColumn("Exame", width="medium"),
                            "Valor": st.column_config.TextColumn("Valor", width="small"),
                            "Unidade": st.column_config.TextColumn("Unidade", width="small"),
                            "Referência": st.column_config.TextColumn("Referência", width="medium"),
                            "Categoria": st.column_config.TextColumn("Categoria", width="medium")
                        }
                    )

            else:
                st.info("👆 Processe alguns exames para ver os resultados aqui.")

                # Show example
                st.markdown("### 💡 Exemplo de Uso")
                st.markdown("""
                **Cole este exemplo no campo de texto e clique em processar:**

                ```
                Data do exame: 15/03/2024

                HEMOGRAMA COMPLETO
                Hemoglobina: 14.2 g/dL
                Hematócrito: 42%
                Leucócitos: 7.500/mm³
                Plaquetas: 280.000/mm³

                BIOQUÍMICA SÉRICA
                Glicose: 92 mg/dL
                Creatinina: 0.9 mg/dL
                ```
                """)

    with tab2:
        st.subheader("Advanced Analytics - Laboratory Data Analysis")

        # Check if we have processed results
        if 'results' in st.session_state and 'df_historical' in st.session_state:
            results = st.session_state['results']
            df_historical = st.session_state['df_historical']
            historical_data = st.session_state.get('historical_data', {})
            lab_score = st.session_state.get('lab_score', {})

            # Analytics Dashboard
            st.markdown("### Analytics Dashboard")

            # Key Metrics Row
            col1, col2, col3, col4 = st.columns(4)

            with col1:
                total_tests = len(results.raw_data) if results.raw_data else 0
                st.metric(
                    label="Total Tests",
                    value=total_tests,
                    delta=f"+{total_tests}" if total_tests > 0 else None
                )

            with col2:
                normal_tests = sum(1 for r in results.raw_data if r.status == "normal") if results.raw_data else 0
                st.metric(
                    label="Normal Results",
                    value=normal_tests,
                    delta=f"{(normal_tests/total_tests*100):.1f}%" if total_tests > 0 else "0%"
                )

            with col3:
                abnormal_tests = total_tests - normal_tests
                st.metric(
                    label="Abnormal Results",
                    value=abnormal_tests,
                    delta=f"-{abnormal_tests}" if abnormal_tests > 0 else None,
                    delta_color="inverse"
                )

            with col4:
                health_score = lab_score.get('score', 0) if lab_score else 0
                st.metric(
                    label="Health Score",
                    value=f"{health_score}%",
                    delta=f"Grade: {lab_score.get('grade', 'N/A').split()[0]}" if lab_score else None
                )

            st.markdown("---")

            # Analytics Tabs
            analytics_tab1, analytics_tab2, analytics_tab3, analytics_tab4 = st.tabs([
                "Trends", "Correlations", "Distributions", "Insights"
            ])

            with analytics_tab1:
                st.markdown("#### 📈 Análise de Tendências Temporais")

                if not df_historical.empty:
                    # Test selection for trends
                    available_tests = df_historical['Exame'].unique().tolist()
                    selected_tests = st.multiselect(
                        "Selecione os exames para análise de tendências:",
                        available_tests,
                        default=available_tests[:3] if len(available_tests) >= 3 else available_tests,
                        help="Escolha até 5 exames para visualizar tendências"
                    )

                    if selected_tests:
                        # Generate trends chart
                        trends_fig = AdvancedAnalytics.generate_trends_chart(df_historical, selected_tests)
                        if trends_fig.data:
                            st.plotly_chart(trends_fig, use_container_width=True)

                            # Trend Analysis Summary
                            st.markdown("##### 📊 Resumo das Tendências")
                            for test in selected_tests:
                                test_data = df_historical[df_historical['Exame'] == test]
                                if len(test_data) >= 2:
                                    latest_value = test_data.iloc[0]['Valor']
                                    previous_value = test_data.iloc[1]['Valor']
                                    change = ((latest_value - previous_value) / previous_value) * 100

                                    trend_emoji = "📈" if change > 5 else "📉" if change < -5 else "➡️"
                                    st.write(f"{trend_emoji} **{test}**: {change:+.1f}% de variação")
                        else:
                            st.info("Dados insuficientes para gerar gráfico de tendências.")
                    else:
                        st.warning("Selecione pelo menos um exame para análise.")
                else:
                    st.info("📊 Nenhum dado histórico encontrado. Processe exames com dados temporais para ver tendências.")

                    # Show example of historical data format
                    st.markdown("##### 💡 Formato de Dados Históricos")
                    st.code("""
Exemplo de formato para análise temporal:

Hemoglobina 15/01/2024: 14.2
Hemoglobina 15/02/2024: 13.8
Hemoglobina 15/03/2024: 14.5

Glicose 15/01/2024: 95
Glicose 15/02/2024: 102
Glicose 15/03/2024: 88
                    """)

            with analytics_tab2:
                st.markdown("#### 🔥 Matriz de Correlação entre Exames")

                if not df_historical.empty and len(df_historical['Exame'].unique()) >= 2:
                    # Generate correlation heatmap
                    correlation_img = AdvancedAnalytics.generate_correlation_heatmap(df_historical)

                    if correlation_img:
                        st.image(f"data:image/png;base64,{correlation_img}", use_column_width=True)

                        # Correlation insights
                        st.markdown("##### 🎯 Insights de Correlação")

                        # Calculate correlations
                        pivot_df = df_historical.pivot(index='Data', columns='Exame', values='Valor')
                        if pivot_df.shape[1] >= 2:
                            corr_matrix = pivot_df.corr()

                            # Find strongest correlations
                            correlations = []
                            for i in range(len(corr_matrix.columns)):
                                for j in range(i+1, len(corr_matrix.columns)):
                                    corr_value = corr_matrix.iloc[i, j]
                                    if not pd.isna(corr_value):
                                        correlations.append({
                                            'exam1': corr_matrix.columns[i],
                                            'exam2': corr_matrix.columns[j],
                                            'correlation': corr_value
                                        })

                            # Sort by absolute correlation
                            correlations.sort(key=lambda x: abs(x['correlation']), reverse=True)

                            # Display top correlations
                            st.markdown("**Correlações mais fortes:**")
                            for corr in correlations[:5]:
                                strength = "Forte" if abs(corr['correlation']) > 0.7 else "Moderada" if abs(corr['correlation']) > 0.4 else "Fraca"
                                direction = "positiva" if corr['correlation'] > 0 else "negativa"
                                emoji = "🔴" if abs(corr['correlation']) > 0.7 else "🟡" if abs(corr['correlation']) > 0.4 else "🟢"

                                st.write(f"{emoji} **{corr['exam1']}** ↔ **{corr['exam2']}**: {corr['correlation']:.3f} ({strength} {direction})")
                    else:
                        st.error("Erro ao gerar matriz de correlação.")
                else:
                    st.info("🔥 Dados insuficientes para análise de correlação. Necessário pelo menos 2 exames com dados históricos.")

            with analytics_tab3:
                st.markdown("#### 📊 Análise de Distribuições e Padrões")

                if results.raw_data:
                    # Category distribution
                    col1, col2 = st.columns(2)

                    with col1:
                        st.markdown("##### 🎯 Distribuição por Categoria")
                        category_counts = {}
                        for result in results.raw_data:
                            category = result.category.title()
                            category_counts[category] = category_counts.get(category, 0) + 1

                        # Create pie chart
                        fig_pie = px.pie(
                            values=list(category_counts.values()),
                            names=list(category_counts.keys()),
                            title="Distribuição de Exames por Categoria"
                        )
                        fig_pie.update_traces(textposition='inside', textinfo='percent+label')
                        st.plotly_chart(fig_pie, use_container_width=True)

                    with col2:
                        st.markdown("##### ⚠️ Status dos Resultados")
                        status_counts = {'Normal': 0, 'Alto': 0, 'Baixo': 0}
                        for result in results.raw_data:
                            if result.status == 'normal':
                                status_counts['Normal'] += 1
                            elif result.status == 'high':
                                status_counts['Alto'] += 1
                            elif result.status == 'low':
                                status_counts['Baixo'] += 1

                        # Create bar chart
                        fig_bar = px.bar(
                            x=list(status_counts.keys()),
                            y=list(status_counts.values()),
                            title="Status dos Resultados",
                            color=list(status_counts.keys()),
                            color_discrete_map={'Normal': 'green', 'Alto': 'red', 'Baixo': 'blue'}
                        )
                        st.plotly_chart(fig_bar, use_container_width=True)

                    # Detailed analysis table
                    st.markdown("##### 📋 Análise Detalhada por Categoria")

                    category_analysis = {}
                    for result in results.raw_data:
                        cat = result.category.title()
                        if cat not in category_analysis:
                            category_analysis[cat] = {'total': 0, 'normal': 0, 'abnormal': 0, 'tests': []}

                        category_analysis[cat]['total'] += 1
                        category_analysis[cat]['tests'].append(result.test_name)

                        if result.status == 'normal':
                            category_analysis[cat]['normal'] += 1
                        else:
                            category_analysis[cat]['abnormal'] += 1

                    # Create analysis dataframe
                    analysis_data = []
                    for cat, data in category_analysis.items():
                        normal_pct = (data['normal'] / data['total']) * 100 if data['total'] > 0 else 0
                        analysis_data.append({
                            'Categoria': cat,
                            'Total': data['total'],
                            'Normais': data['normal'],
                            'Alterados': data['abnormal'],
                            '% Normal': f"{normal_pct:.1f}%",
                            'Exames': ', '.join(data['tests'][:3]) + ('...' if len(data['tests']) > 3 else '')
                        })

                    df_analysis = pd.DataFrame(analysis_data)
                    st.dataframe(df_analysis, use_container_width=True)

                else:
                    st.info("📊 Processe alguns exames para ver análise de distribuições.")

            with analytics_tab4:
                st.markdown("#### 🎯 Insights Inteligentes e Recomendações")

                if results.raw_data:
                    # AI-powered insights
                    st.markdown("##### 🤖 Insights Gerados por IA")

                    # Critical findings
                    critical_findings = [r for r in results.raw_data if r.status in ['high', 'low']]
                    if critical_findings:
                        st.markdown("**🚨 Achados Críticos:**")
                        for finding in critical_findings[:5]:
                            status_emoji = "🔴" if finding.status == "high" else "🔵"
                            st.write(f"{status_emoji} **{finding.test_name}**: {finding.value} {finding.unit} ({finding.status.upper()})")

                    # Health score breakdown
                    if lab_score:
                        st.markdown("##### 🏆 Análise do Score de Saúde")

                        score_color = lab_score.get('color', 'gray')
                        st.markdown(f"""
                        <div style="background: linear-gradient(135deg, {score_color}, white);
                                   padding: 1rem; border-radius: 10px; margin: 1rem 0;">
                            <h3 style="color: white; text-align: center;">
                                Score: {lab_score.get('score', 0)}% - {lab_score.get('grade', 'N/A')}
                            </h3>
                            <p style="color: white; text-align: center;">
                                {lab_score.get('summary', 'Sem dados')}
                            </p>
                        </div>
                        """, unsafe_allow_html=True)

                    # Recommendations based on patterns
                    st.markdown("##### 💡 Recomendações Personalizadas")

                    recommendations = []

                    # Check for common patterns
                    high_glucose = any(r.test_name.lower() == 'glicose' and r.status == 'high' for r in results.raw_data)
                    high_cholesterol = any('colesterol' in r.test_name.lower() and r.status == 'high' for r in results.raw_data)
                    low_hemoglobin = any('hemoglobina' in r.test_name.lower() and r.status == 'low' for r in results.raw_data)

                    if high_glucose:
                        recommendations.append("🍎 **Glicose elevada**: Considere dieta com baixo índice glicêmico e exercícios regulares")

                    if high_cholesterol:
                        recommendations.append("❤️ **Colesterol alto**: Reduza gorduras saturadas e aumente fibras na dieta")

                    if low_hemoglobin:
                        recommendations.append("🥩 **Hemoglobina baixa**: Inclua alimentos ricos em ferro e vitamina C")

                    if len(critical_findings) > 3:
                        recommendations.append("⚠️ **Múltiplas alterações**: Agende consulta médica para avaliação completa")

                    if not recommendations:
                        recommendations.append("✅ **Parabéns!** Seus exames estão dentro dos padrões normais")

                    for rec in recommendations:
                        st.markdown(f"- {rec}")

                    # Follow-up suggestions
                    st.markdown("##### 📅 Sugestões de Acompanhamento")

                    follow_up = []
                    if critical_findings:
                        follow_up.append("🔄 **Repetir exames alterados** em 30-60 dias")
                        follow_up.append("👨‍⚕️ **Consulta médica** para interpretação detalhada")

                    if len(results.raw_data) < 10:
                        follow_up.append("🧪 **Exames complementares** podem ser necessários")

                    follow_up.append("📊 **Monitoramento regular** para acompanhar tendências")

                    for suggestion in follow_up:
                        st.markdown(f"- {suggestion}")

                    # Export analytics report
                    st.markdown("##### 📄 Relatório de Analytics")

                    analytics_report = {
                        "timestamp": datetime.now().isoformat(),
                        "total_tests": len(results.raw_data),
                        "normal_tests": len([r for r in results.raw_data if r.status == 'normal']),
                        "abnormal_tests": len(critical_findings),
                        "health_score": lab_score,
                        "critical_findings": [
                            {
                                "test": f.test_name,
                                "value": f.value,
                                "unit": f.unit,
                                "status": f.status,
                                "reference": f.reference_range
                            } for f in critical_findings
                        ],
                        "recommendations": recommendations,
                        "follow_up": follow_up
                    }

                    st.download_button(
                        label="📊 Download Relatório Analytics",
                        data=json.dumps(analytics_report, indent=2, ensure_ascii=False),
                        file_name=f"analytics_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                        mime="application/json",
                        use_container_width=True
                    )

                else:
                    st.info("🎯 Processe alguns exames para ver insights inteligentes.")

        else:
            # No data available - show analytics demo
            st.info("📊 Processe alguns exames na aba 'Processamento' para ver analytics avançados aqui.")

            # Demo analytics features
            st.markdown("### 🎯 Funcionalidades do Analytics Ultra")

            col1, col2 = st.columns(2)

            with col1:
                st.markdown("""
                #### 📈 Análise de Tendências
                - Gráficos temporais interativos
                - Comparação entre múltiplos exames
                - Detecção de padrões e variações
                - Linhas de referência automáticas

                #### 🔥 Correlações
                - Matriz de correlação visual
                - Identificação de relacionamentos
                - Análise de dependências
                - Insights sobre interações
                """)

            with col2:
                st.markdown("""
                #### 📊 Distribuições
                - Análise por categorias
                - Status dos resultados
                - Padrões de normalidade
                - Visualizações interativas

                #### 🎯 Insights IA
                - Recomendações personalizadas
                - Score de saúde inteligente
                - Sugestões de acompanhamento
                - Relatórios detalhados
                """)

    with tab3:
        st.subheader("Medical Education Center - Advanced Learning Platform")

        # Education main tabs
        preceptor_tab1, preceptor_tab2, preceptor_tab3, preceptor_tab4, preceptor_tab5 = st.tabs([
            "Knowledge Base", "Clinical Cases", "Simulator", "Reference Guides", "Assessment"
        ])

        with preceptor_tab1:
            st.markdown("#### 📚 Biblioteca de Conhecimento Laboratorial")

            # Knowledge categories
            knowledge_category = st.selectbox(
                "Escolha uma categoria de estudo:",
                [
                    "🩸 Hemograma Completo",
                    "🧪 Bioquímica Clínica",
                    "💊 Função Hepática",
                    "❤️ Perfil Lipídico",
                    "🦴 Função Renal",
                    "🔬 Endocrinologia",
                    "🛡️ Imunologia",
                    "⚡ Marcadores Cardíacos",
                    "🩺 Coagulação",
                    "💉 Eletrólitos"
                ]
            )

            # Dynamic content based on selection
            if knowledge_category == "🩸 Hemograma Completo":
                col1, col2 = st.columns([2, 1])

                with col1:
                    st.markdown("""
                    ##### 🔬 Hemograma: Fundamentos e Interpretação

                    **📊 Componentes Principais:**

                    **1. Série Vermelha (Eritrograma)**
                    - **Hemoglobina (Hb)**: 13.5-17.5 g/dL (♂) | 12.0-15.5 g/dL (♀)
                      - Função: Transporte de O₂ e CO₂
                      - ↑ Policitemia, desidratação, DPOC
                      - ↓ Anemia, hemorragia, deficiência nutricional

                    - **Hematócrito (Ht)**: 41-53% (♂) | 36-46% (♀)
                      - Percentual de volume ocupado pelos eritrócitos
                      - Correlação direta com hemoglobina

                    - **VCM (Volume Corpuscular Médio)**: 80-100 fL
                      - Microcítica (<80): Anemia ferropriva, talassemia
                      - Normocítica (80-100): Anemia de doença crônica
                      - Macrocítica (>100): Deficiência B12/folato, alcoolismo

                    **2. Série Branca (Leucograma)**
                    - **Leucócitos Totais**: 4.000-11.000/mm³
                    - **Neutrófilos**: 1.800-7.700/mm³ (50-70%)
                      - Primeira linha de defesa contra bactérias
                      - ↑ Infecções bacterianas, estresse, corticoides
                      - ↓ Infecções virais, quimioterapia, aplasia medular

                    - **Linfócitos**: 1.000-4.800/mm³ (20-40%)
                      - Imunidade celular e humoral
                      - ↑ Infecções virais, leucemias linfoides
                      - ↓ Imunossupressão, corticoides, HIV
                    """)

                with col2:
                    st.markdown("""
                    ##### 🎯 Dicas de Interpretação

                    **🚨 Alertas Críticos:**
                    - Hb < 7 g/dL: Transfusão
                    - Leucócitos > 50.000: Leucemia?
                    - Plaquetas < 20.000: Risco hemorrágico

                    **🔍 Padrões Clássicos:**
                    - **Anemia Ferropriva**: ↓Hb, ↓VCM, ↓Ferritina
                    - **Anemia Megaloblástica**: ↓Hb, ↑VCM, ↓B12/Folato
                    - **Infecção Bacteriana**: ↑Neutrófilos, ↑Bastões
                    - **Infecção Viral**: ↑Linfócitos, ↓Neutrófilos

                    **📈 Valores Pediátricos:**
                    - RN: Hb 14-20 g/dL
                    - 6m-2a: Hb 10.5-13.5 g/dL
                    - 2-12a: Hb 11.5-15.5 g/dL
                    """)

                # Interactive hemogram simulator
                st.markdown("##### 🎮 Simulador Interativo de Hemograma")

                sim_col1, sim_col2, sim_col3 = st.columns(3)

                with sim_col1:
                    sim_hb = st.slider("Hemoglobina (g/dL)", 5.0, 20.0, 14.0, 0.1)
                    sim_ht = st.slider("Hematócrito (%)", 15, 60, 42, 1)
                    sim_vcm = st.slider("VCM (fL)", 60, 120, 90, 1)

                with sim_col2:
                    sim_leuco = st.slider("Leucócitos (/mm³)", 1000, 50000, 7000, 100)
                    sim_neutro = st.slider("Neutrófilos (%)", 20, 90, 60, 1)
                    sim_linfo = st.slider("Linfócitos (%)", 10, 70, 30, 1)

                with sim_col3:
                    sim_plaq = st.slider("Plaquetas (/mm³)", 50000, 800000, 250000, 10000)

                    # Auto-interpretation using all values
                    interpretation = []

                    # Hemoglobina analysis
                    if sim_hb < 12:
                        interpretation.append("🔴 Anemia")
                    elif sim_hb > 17:
                        interpretation.append("🔴 Policitemia")
                    else:
                        interpretation.append("✅ Hemoglobina normal")

                    # Hematócrito analysis
                    if sim_ht < 36:
                        interpretation.append("🔵 Hematócrito baixo")
                    elif sim_ht > 50:
                        interpretation.append("🔴 Hematócrito alto")

                    # VCM analysis
                    if sim_vcm < 80:
                        interpretation.append("🔵 Microcitose")
                    elif sim_vcm > 100:
                        interpretation.append("🔵 Macrocitose")

                    # Leucócitos analysis
                    if sim_leuco > 11000:
                        interpretation.append("⚠️ Leucocitose")
                    elif sim_leuco < 4000:
                        interpretation.append("⚠️ Leucopenia")

                    # Neutrófilos analysis
                    if sim_neutro > 70:
                        interpretation.append("⚠️ Neutrofilia")
                    elif sim_neutro < 50:
                        interpretation.append("⚠️ Neutropenia")

                    # Linfócitos analysis
                    if sim_linfo > 40:
                        interpretation.append("⚠️ Linfocitose")
                    elif sim_linfo < 20:
                        interpretation.append("⚠️ Linfopenia")

                    # Plaquetas analysis
                    if sim_plaq < 150000:
                        interpretation.append("🔴 Plaquetopenia")
                    elif sim_plaq > 450000:
                        interpretation.append("🔴 Plaquetose")

                    st.markdown("**🎯 Interpretação:**")
                    for interp in interpretation:
                        st.write(f"- {interp}")

            elif knowledge_category == "🧪 Bioquímica Clínica":
                st.markdown("""
                ##### 🧪 Bioquímica Clínica: Metabolismo e Função Orgânica

                **🍯 Metabolismo da Glicose**
                - **Glicemia de Jejum**: 70-99 mg/dL
                  - 100-125 mg/dL: Pré-diabetes
                  - ≥126 mg/dL: Diabetes (confirmar em 2 ocasiões)
                - **HbA1c**: <5.7% (normal) | 5.7-6.4% (pré-diabetes) | ≥6.5% (diabetes)
                - **TOTG 75g**: <140 mg/dL (2h normal) | 140-199 mg/dL (intolerância) | ≥200 mg/dL (diabetes)

                **🔋 Função Renal**
                - **Creatinina**: 0.6-1.2 mg/dL
                  - Produto do metabolismo muscular
                  - Filtração glomerular (TFG = 140-idade × peso / 72 × creatinina)
                - **Ureia**: 15-45 mg/dL
                  - Produto do metabolismo proteico
                  - ↑ Desidratação, insuficiência renal, dieta hiperproteica
                - **Ácido Úrico**: 3.5-7.2 mg/dL
                  - ↑ Gota, síndrome metabólica, insuficiência renal

                **⚡ Eletrólitos Essenciais**
                - **Sódio (Na+)**: 135-145 mEq/L
                  - Hiponatremia: <135 (SIADH, diuréticos, insuficiência cardíaca)
                  - Hipernatremia: >145 (desidratação, diabetes insípido)
                - **Potássio (K+)**: 3.5-5.0 mEq/L
                  - Hipocalemia: <3.5 (diuréticos, diarreia, hiperaldosteronismo)
                  - Hipercalemia: >5.0 (insuficiência renal, ACE-inibidores)
                """)

                # Clinical calculator
                st.markdown("##### 🧮 Calculadora Clínica")

                calc_col1, calc_col2 = st.columns(2)

                with calc_col1:
                    st.markdown("**📊 Clearance de Creatinina (Cockcroft-Gault)**")
                    age = st.number_input("Idade (anos)", 18, 100, 50)
                    weight = st.number_input("Peso (kg)", 40, 150, 70)
                    creatinine = st.number_input("Creatinina (mg/dL)", 0.5, 10.0, 1.0, 0.1)
                    gender = st.selectbox("Sexo", ["Masculino", "Feminino"])

                    # Calculate clearance
                    clearance = ((140 - age) * weight) / (72 * creatinine)
                    if gender == "Feminino":
                        clearance *= 0.85

                    st.metric("Clearance de Creatinina", f"{clearance:.1f} mL/min")

                    if clearance >= 90:
                        st.success("✅ Função renal normal")
                    elif clearance >= 60:
                        st.warning("⚠️ Disfunção renal leve")
                    elif clearance >= 30:
                        st.warning("🔶 Disfunção renal moderada")
                    else:
                        st.error("🔴 Disfunção renal grave")

                with calc_col2:
                    st.markdown("**🎯 Osmolalidade Sérica**")
                    sodium = st.number_input("Sódio (mEq/L)", 120, 160, 140)
                    glucose = st.number_input("Glicose (mg/dL)", 70, 400, 90)
                    urea = st.number_input("Ureia (mg/dL)", 10, 100, 30)

                    # Calculate osmolality
                    osmolality = 2 * sodium + glucose/18 + urea/2.8

                    st.metric("Osmolalidade Calculada", f"{osmolality:.1f} mOsm/kg")

                    if 280 <= osmolality <= 295:
                        st.success("✅ Osmolalidade normal")
                    else:
                        st.warning("⚠️ Osmolalidade alterada")

        with preceptor_tab2:
            st.markdown("#### 🧠 Casos Clínicos Interativos")

            # Case selection
            case_selection = st.selectbox(
                "Escolha um caso clínico para análise:",
                [
                    "👨‍💼 Caso 1: Executivo com Fadiga",
                    "👵 Caso 2: Idosa com Confusão Mental",
                    "🏃‍♂️ Caso 3: Atleta com Dor Torácica",
                    "👶 Caso 4: Criança com Palidez",
                    "👩‍🦳 Caso 5: Mulher com Palpitações"
                ]
            )

            if case_selection == "👨‍💼 Caso 1: Executivo com Fadiga":
                st.markdown("""
                ##### 📋 História Clínica
                **Paciente:** João, 45 anos, executivo
                **Queixa Principal:** Fadiga progressiva há 3 meses
                **HDA:** Cansaço excessivo, sonolência pós-prandial, ganho de peso (5kg), constipação
                **Antecedentes:** HAS controlada, pai com DM2
                **Medicações:** Losartana 50mg/dia
                **Exame Físico:** PA 140/90 mmHg, IMC 28, sem outras alterações
                """)

                # Show lab results
                st.markdown("##### 🧪 Resultados Laboratoriais")

                case1_results = {
                    "Glicemia de jejum": "126 mg/dL",
                    "HbA1c": "7.2%",
                    "Colesterol total": "240 mg/dL",
                    "LDL": "160 mg/dL",
                    "HDL": "35 mg/dL",
                    "Triglicerídeos": "280 mg/dL",
                    "Creatinina": "1.1 mg/dL",
                    "TSH": "8.5 mUI/L",
                    "T4 livre": "0.6 ng/dL"
                }

                for test, value in case1_results.items():
                    st.write(f"• **{test}**: {value}")

                # Interactive analysis
                st.markdown("##### 🎯 Análise Interativa")

                analysis_step = st.radio(
                    "Escolha o próximo passo da análise:",
                    [
                        "1️⃣ Identificar alterações",
                        "2️⃣ Formular hipóteses diagnósticas",
                        "3️⃣ Solicitar exames complementares",
                        "4️⃣ Propor tratamento"
                    ]
                )

                if analysis_step == "1️⃣ Identificar alterações":
                    st.success("""
                    **🔍 Alterações Identificadas:**
                    - ✅ Glicemia de jejum: 126 mg/dL (↑ - sugestivo de DM)
                    - ✅ HbA1c: 7.2% (↑ - confirma DM, controle inadequado)
                    - ✅ Dislipidemia: CT↑, LDL↑, HDL↓, TG↑
                    - ✅ TSH: 8.5 mUI/L (↑ - hipotireoidismo)
                    - ✅ T4 livre: 0.6 ng/dL (↓ - confirma hipotireoidismo)
                    """)

                elif analysis_step == "2️⃣ Formular hipóteses diagnósticas":
                    st.info("""
                    **🎯 Hipóteses Diagnósticas:**
                    1. **Diabetes Mellitus tipo 2** (glicemia ≥126 mg/dL + HbA1c ≥6.5%)
                    2. **Hipotireoidismo primário** (TSH↑ + T4L↓)
                    3. **Dislipidemia mista** (síndrome metabólica)
                    4. **Síndrome metabólica** (DM + dislipidemia + HAS + obesidade)

                    **🔗 Correlação:** Hipotireoidismo pode agravar resistência insulínica e dislipidemia
                    """)

                elif analysis_step == "3️⃣ Solicitar exames complementares":
                    st.warning("""
                    **🧪 Exames Complementares Sugeridos:**
                    - **Microalbuminúria** (rastreio nefropatia diabética)
                    - **Fundoscopia** (rastreio retinopatia diabética)
                    - **ECG** (rastreio cardiopatia isquêmica)
                    - **Anti-TPO** (investigar tireoidite autoimune)
                    - **Perfil lipídico completo** (apo A1, apo B)
                    - **Hemoglobina glicada** (controle a cada 3 meses)
                    """)

                elif analysis_step == "4️⃣ Propor tratamento":
                    st.error("""
                    **💊 Plano Terapêutico:**

                    **Diabetes:**
                    - Metformina 850mg 2x/dia (primeira linha)
                    - Orientação nutricional (dieta hipocalórica)
                    - Atividade física regular
                    - Meta: HbA1c <7%

                    **Hipotireoidismo:**
                    - Levotiroxina 50-75mcg/dia (jejum)
                    - Controle TSH em 6-8 semanas
                    - Meta: TSH 0.4-4.0 mUI/L

                    **Dislipidemia:**
                    - Atorvastatina 20mg/dia
                    - Meta: LDL <100 mg/dL (diabético)
                    """)

        with preceptor_tab3:
            st.markdown("#### 🎯 Simulador de Interpretação Laboratorial")

            # Simulator mode selection
            sim_mode = st.selectbox(
                "Escolha o modo de simulação:",
                [
                    "🎮 Modo Treino - Valores Aleatórios",
                    "🎯 Modo Desafio - Casos Complexos",
                    "📊 Modo Personalizado - Seus Valores"
                ]
            )

            if sim_mode == "🎮 Modo Treino - Valores Aleatórios":
                if st.button("🎲 Gerar Caso Aleatório", type="primary"):
                    import random

                    # Generate random lab values
                    random_case = {
                        "Hemoglobina": round(random.uniform(8.0, 18.0), 1),
                        "Leucócitos": random.randint(2000, 25000),
                        "Plaquetas": random.randint(80000, 600000),
                        "Glicose": random.randint(60, 300),
                        "Creatinina": round(random.uniform(0.5, 3.0), 1),
                        "TSH": round(random.uniform(0.1, 15.0), 1),
                        "Colesterol": random.randint(120, 350)
                    }

                    st.markdown("##### 🧪 Caso Gerado:")

                    col1, col2 = st.columns(2)

                    with col1:
                        for test, value in list(random_case.items())[:4]:
                            # Determine if abnormal
                            normal_ranges = {
                                "Hemoglobina": (12.0, 17.0),
                                "Leucócitos": (4000, 11000),
                                "Plaquetas": (150000, 450000),
                                "Glicose": (70, 99)
                            }

                            min_val, max_val = normal_ranges.get(test, (0, 999999))
                            status = "🔴" if value < min_val or value > max_val else "✅"

                            st.write(f"{status} **{test}**: {value}")

                    with col2:
                        for test, value in list(random_case.items())[4:]:
                            normal_ranges = {
                                "Creatinina": (0.6, 1.2),
                                "TSH": (0.4, 4.0),
                                "Colesterol": (0, 200)
                            }

                            min_val, max_val = normal_ranges.get(test, (0, 999999))
                            status = "🔴" if value < min_val or value > max_val else "✅"

                            st.write(f"{status} **{test}**: {value}")

                    # Interactive interpretation
                    st.markdown("##### 🎯 Sua Interpretação:")

                    user_interpretation = st.text_area(
                        "Descreva sua interpretação dos resultados:",
                        placeholder="Ex: Paciente apresenta anemia microcítica, leucocitose com desvio à esquerda..."
                    )

                    if st.button("✅ Verificar Interpretação"):
                        if user_interpretation:
                            st.success("🎓 Interpretação registrada! Continue praticando para aprimorar suas habilidades.")
                        else:
                            st.warning("⚠️ Digite sua interpretação para continuar.")

            elif sim_mode == "🎯 Modo Desafio - Casos Complexos":
                st.markdown("##### 🏆 Desafio Clínico Avançado")

                challenge_case = st.selectbox(
                    "Escolha um desafio:",
                    [
                        "🔥 Desafio 1: Pancitopenia Misteriosa",
                        "⚡ Desafio 2: Síndrome Metabólica Complexa",
                        "🧬 Desafio 3: Distúrbio Endócrino Múltiplo"
                    ]
                )

                if challenge_case == "🔥 Desafio 1: Pancitopenia Misteriosa":
                    st.markdown("""
                    **👤 Paciente:** Maria, 35 anos, professora
                    **Sintomas:** Fadiga extrema, equimoses espontâneas, infecções recorrentes

                    **🧪 Resultados:**
                    - Hemoglobina: 6.8 g/dL
                    - Leucócitos: 2.100/mm³
                    - Neutrófilos: 800/mm³
                    - Plaquetas: 45.000/mm³
                    - VCM: 105 fL
                    - Reticulócitos: 0.5%
                    - LDH: 850 U/L
                    - Bilirrubina indireta: 2.8 mg/dL
                    """)

                    # Multiple choice questions
                    q1 = st.radio(
                        "**Pergunta 1:** Qual o diagnóstico mais provável?",
                        [
                            "A) Anemia ferropriva severa",
                            "B) Anemia aplástica",
                            "C) Anemia megaloblástica",
                            "D) Leucemia aguda"
                        ]
                    )

                    if q1 == "B) Anemia aplástica":
                        st.success("✅ Correto! Pancitopenia + reticulócitos baixos + medula hipocelular = Anemia aplástica")
                    elif q1:
                        st.error("❌ Incorreto. Revise os conceitos de pancitopenia.")

                    q2 = st.radio(
                        "**Pergunta 2:** Qual exame é essencial para confirmação?",
                        [
                            "A) Mielograma com biópsia de medula óssea",
                            "B) Eletroforese de hemoglobina",
                            "C) Dosagem de B12 e folato",
                            "D) Teste de Coombs"
                        ]
                    )

                    if q2 == "A) Mielograma com biópsia de medula óssea":
                        st.success("✅ Correto! Biópsia mostrará medula hipocelular (<25% celularidade)")
                    elif q2:
                        st.error("❌ Incorreto. Pense no exame que avalia a medula óssea.")

        with preceptor_tab4:
            st.markdown("#### 📖 Guias de Referência Rápida")

            # Quick reference guides
            guide_category = st.selectbox(
                "Escolha um guia de referência:",
                [
                    "📊 Valores de Referência Completos",
                    "🚨 Valores Críticos e Alertas",
                    "🔄 Algoritmos Diagnósticos",
                    "💊 Interferências Medicamentosas",
                    "🎯 Interpretação por Faixa Etária"
                ]
            )

            if guide_category == "📊 Valores de Referência Completos":
                st.markdown("##### 📋 Tabela Completa de Valores de Referência")

                # Create comprehensive reference table
                reference_data = [
                    {"Exame": "Hemoglobina", "Homem": "13.5-17.5 g/dL", "Mulher": "12.0-15.5 g/dL", "Criança": "11.0-14.0 g/dL"},
                    {"Exame": "Hematócrito", "Homem": "41-53%", "Mulher": "36-46%", "Criança": "33-42%"},
                    {"Exame": "Leucócitos", "Homem": "4.000-11.000/mm³", "Mulher": "4.000-11.000/mm³", "Criança": "5.000-15.000/mm³"},
                    {"Exame": "Plaquetas", "Homem": "150.000-450.000/mm³", "Mulher": "150.000-450.000/mm³", "Criança": "150.000-450.000/mm³"},
                    {"Exame": "Glicose", "Homem": "70-99 mg/dL", "Mulher": "70-99 mg/dL", "Criança": "70-99 mg/dL"},
                    {"Exame": "Creatinina", "Homem": "0.7-1.3 mg/dL", "Mulher": "0.6-1.1 mg/dL", "Criança": "0.3-0.7 mg/dL"},
                    {"Exame": "TSH", "Homem": "0.4-4.0 mUI/L", "Mulher": "0.4-4.0 mUI/L", "Criança": "0.7-6.4 mUI/L"},
                    {"Exame": "Colesterol Total", "Homem": "<200 mg/dL", "Mulher": "<200 mg/dL", "Criança": "<170 mg/dL"},
                ]

                df_reference = pd.DataFrame(reference_data)
                st.dataframe(df_reference, use_container_width=True)

                # Download reference guide
                st.download_button(
                    label="📥 Download Guia Completo (PDF)",
                    data="Guia de Valores de Referência - LabsForm Pro Ultra",
                    file_name="valores_referencia.txt",
                    mime="text/plain"
                )

            elif guide_category == "🚨 Valores Críticos e Alertas":
                st.markdown("##### 🚨 Valores Críticos que Requerem Ação Imediata")

                col1, col2 = st.columns(2)

                with col1:
                    st.markdown("""
                    **🔴 CRÍTICOS - Notificação Imediata:**

                    **Hematologia:**
                    - Hemoglobina < 7.0 g/dL
                    - Leucócitos < 1.000/mm³ ou > 50.000/mm³
                    - Plaquetas < 20.000/mm³
                    - Blastos > 20% (suspeita leucemia)

                    **Bioquímica:**
                    - Glicose < 50 mg/dL ou > 400 mg/dL
                    - Potássio < 2.5 mEq/L ou > 6.0 mEq/L
                    - Sódio < 120 mEq/L ou > 160 mEq/L
                    - Creatinina > 5.0 mg/dL
                    """)

                with col2:
                    st.markdown("""
                    **🟡 ATENÇÃO - Monitoramento Próximo:**

                    **Cardíaco:**
                    - Troponina > 0.04 ng/mL
                    - CK-MB > 6.3 ng/mL
                    - D-dímero > 500 ng/mL

                    **Coagulação:**
                    - INR > 4.0
                    - TTPA > 100 segundos

                    **Endócrino:**
                    - TSH > 10.0 mUI/L
                    - Cortisol < 3.0 μg/dL (manhã)
                    """)

                # Critical values calculator
                st.markdown("##### 🧮 Calculadora de Risco")

                risk_hb = st.number_input("Hemoglobina atual (g/dL):", 3.0, 20.0, 12.0, 0.1)
                risk_k = st.number_input("Potássio atual (mEq/L):", 1.0, 8.0, 4.0, 0.1)
                risk_glucose = st.number_input("Glicose atual (mg/dL):", 20, 600, 90)

                # Calculate risk score
                risk_score = 0
                alerts = []

                if risk_hb < 7.0:
                    risk_score += 3
                    alerts.append("🔴 CRÍTICO: Hemoglobina muito baixa - Risco de hipóxia tecidual")
                elif risk_hb < 10.0:
                    risk_score += 1
                    alerts.append("🟡 ATENÇÃO: Anemia moderada")

                if risk_k < 2.5 or risk_k > 6.0:
                    risk_score += 3
                    alerts.append("🔴 CRÍTICO: Potássio em nível perigoso - Risco de arritmia")
                elif risk_k < 3.5 or risk_k > 5.0:
                    risk_score += 1
                    alerts.append("🟡 ATENÇÃO: Potássio alterado")

                if risk_glucose < 50 or risk_glucose > 400:
                    risk_score += 3
                    alerts.append("🔴 CRÍTICO: Glicose em nível perigoso")
                elif risk_glucose > 200:
                    risk_score += 1
                    alerts.append("🟡 ATENÇÃO: Hiperglicemia")

                # Display risk assessment
                if risk_score >= 3:
                    st.error(f"🚨 RISCO ALTO (Score: {risk_score}) - Ação imediata necessária!")
                elif risk_score > 0:
                    st.warning(f"⚠️ RISCO MODERADO (Score: {risk_score}) - Monitoramento próximo")
                else:
                    st.success("✅ RISCO BAIXO - Valores dentro de limites seguros")

                for alert in alerts:
                    st.write(f"• {alert}")

            elif guide_category == "🔄 Algoritmos Diagnósticos":
                st.markdown("##### 🔄 Algoritmos de Interpretação Diagnóstica")

                algorithm_type = st.selectbox(
                    "Escolha um algoritmo:",
                    [
                        "🩸 Investigação de Anemia",
                        "🫀 Avaliação de Dor Torácica",
                        "🧠 Investigação de Confusão Mental"
                    ]
                )

                if algorithm_type == "🩸 Investigação de Anemia":
                    st.markdown("""
                    ```
                    ALGORITMO: INVESTIGAÇÃO DE ANEMIA

                    Hemoglobina < 12 g/dL (♀) ou < 13 g/dL (♂)
                                    ↓
                    Avaliar VCM (Volume Corpuscular Médio)
                                    ↓
                    ┌─────────────────┬─────────────────┬─────────────────┐
                    │   VCM < 80 fL   │  VCM 80-100 fL  │   VCM > 100 fL  │
                    │  (Microcítica)  │  (Normocítica)  │  (Macrocítica)  │
                    └─────────────────┴─────────────────┴─────────────────┘
                            ↓                   ↓                   ↓
                    • Ferritina         • Reticulócitos     • B12 / Folato
                    • Ferro sérico      • LDH               • TSH
                    • TIBC              • Bilirrubinas      • Álcool
                    • Eletroforese Hb   • Coombs direto     • Medicamentos
                            ↓                   ↓                   ↓
                    Ferritina < 15:     Reticulócitos ↑:    B12 < 200:
                    → Anemia ferropriva → Hemólise/sangramento → Def. B12

                    Ferritina normal:   Reticulócitos ↓:    Folato < 3:
                    → Talassemia        → Aplasia/infiltração → Def. Folato
                    ```
                    """)

        with preceptor_tab5:
            st.markdown("#### 🏆 Sistema de Avaliação e Certificação")

            # Assessment system
            assessment_mode = st.selectbox(
                "Escolha o tipo de avaliação:",
                [
                    "📝 Quiz Básico - Valores de Referência",
                    "🧠 Avaliação Intermediária - Interpretação",
                    "🏆 Certificação Avançada - Casos Complexos"
                ]
            )

            if assessment_mode == "📝 Quiz Básico - Valores de Referência":
                st.markdown("##### 📝 Quiz: Conhecimentos Básicos")

                if 'quiz_started' not in st.session_state:
                    st.session_state.quiz_started = False
                    st.session_state.quiz_score = 0
                    st.session_state.quiz_question = 0

                if not st.session_state.quiz_started:
                    if st.button("🚀 Iniciar Quiz", type="primary"):
                        st.session_state.quiz_started = True
                        st.session_state.quiz_score = 0
                        st.session_state.quiz_question = 0
                        st.rerun()
                else:
                    # Quiz questions
                    questions = [
                        {
                            "question": "Qual o valor normal de hemoglobina para homens adultos?",
                            "options": ["A) 10.0-14.0 g/dL", "B) 13.5-17.5 g/dL", "C) 15.0-20.0 g/dL", "D) 8.0-12.0 g/dL"],
                            "correct": "B) 13.5-17.5 g/dL"
                        },
                        {
                            "question": "Leucocitose é definida como leucócitos acima de:",
                            "options": ["A) 8.000/mm³", "B) 10.000/mm³", "C) 11.000/mm³", "D) 15.000/mm³"],
                            "correct": "C) 11.000/mm³"
                        },
                        {
                            "question": "O valor normal de glicemia de jejum é:",
                            "options": ["A) 60-90 mg/dL", "B) 70-99 mg/dL", "C) 80-110 mg/dL", "D) 90-120 mg/dL"],
                            "correct": "B) 70-99 mg/dL"
                        },
                        {
                            "question": "TSH elevado indica:",
                            "options": ["A) Hipertireoidismo", "B) Hipotireoidismo", "C) Função normal", "D) Tireoidite"],
                            "correct": "B) Hipotireoidismo"
                        },
                        {
                            "question": "Plaquetopenia é definida como plaquetas abaixo de:",
                            "options": ["A) 100.000/mm³", "B) 150.000/mm³", "C) 200.000/mm³", "D) 250.000/mm³"],
                            "correct": "B) 150.000/mm³"
                        }
                    ]

                    if st.session_state.quiz_question < len(questions):
                        current_q = questions[st.session_state.quiz_question]

                        st.markdown(f"**Pergunta {st.session_state.quiz_question + 1}/5:**")
                        st.markdown(f"**{current_q['question']}**")

                        answer = st.radio("Escolha sua resposta:", current_q['options'], key=f"q_{st.session_state.quiz_question}")

                        if st.button("➡️ Próxima Pergunta"):
                            if answer == current_q['correct']:
                                st.session_state.quiz_score += 1
                                st.success("✅ Correto!")
                            else:
                                st.error(f"❌ Incorreto. Resposta correta: {current_q['correct']}")

                            st.session_state.quiz_question += 1
                            st.rerun()

                    else:
                        # Quiz completed
                        score_percentage = (st.session_state.quiz_score / len(questions)) * 100

                        st.markdown("### 🎉 Quiz Concluído!")
                        st.markdown(f"**Pontuação: {st.session_state.quiz_score}/{len(questions)} ({score_percentage:.1f}%)**")

                        if score_percentage >= 80:
                            st.success("🏆 Excelente! Você demonstra conhecimento sólido dos valores de referência.")
                            st.balloons()
                        elif score_percentage >= 60:
                            st.warning("👍 Bom trabalho! Continue estudando para aprimorar seus conhecimentos.")
                        else:
                            st.error("📚 Recomendamos revisar os conceitos básicos antes de prosseguir.")

                        # Certificate generation
                        if score_percentage >= 80:
                            certificate_data = {
                                "certificado": "Conhecimentos Básicos em Laboratório Clínico",
                                "pontuacao": f"{st.session_state.quiz_score}/{len(questions)}",
                                "percentual": f"{score_percentage:.1f}%",
                                "data": datetime.now().strftime("%d/%m/%Y %H:%M"),
                                "nivel": "Básico"
                            }

                            st.download_button(
                                label="🏆 Download Certificado",
                                data=json.dumps(certificate_data, indent=2, ensure_ascii=False),
                                file_name=f"certificado_basico_{datetime.now().strftime('%Y%m%d')}.json",
                                mime="application/json"
                            )

                        if st.button("🔄 Refazer Quiz"):
                            st.session_state.quiz_started = False
                            st.session_state.quiz_score = 0
                            st.session_state.quiz_question = 0
                            st.rerun()

            elif assessment_mode == "🏆 Certificação Avançada - Casos Complexos":
                st.markdown("##### 🏆 Certificação Profissional Avançada")

                st.info("""
                **📋 Requisitos para Certificação Avançada:**
                - ✅ Completar Quiz Básico com ≥80%
                - ✅ Resolver 3 casos clínicos complexos
                - ✅ Demonstrar interpretação correta de exames
                - ✅ Propor condutas adequadas

                **🎯 Benefícios da Certificação:**
                - 📜 Certificado digital oficial
                - 🏅 Badge de especialista
                - 📊 Relatório detalhado de competências
                - 🔄 Validade de 2 anos
                """)

                if st.button("🚀 Iniciar Processo de Certificação"):
                    st.warning("🚧 Funcionalidade em desenvolvimento. Em breve disponível!")

    with tab4:
        st.subheader("Help & Support Center - Complete Documentation and Assistance")

        # Help main tabs
        help_tab1, help_tab2, help_tab3, help_tab4, help_tab5, help_tab6 = st.tabs([
            "Quick Start", "User Manual", "Troubleshooting", "FAQ", "Tutorials", "Support"
        ])

        with help_tab1:
            st.markdown("#### Quick Start Guide")

            # Quick start wizard
            st.markdown("##### Initial Setup Assistant")

            # Step-by-step setup
            setup_step = st.selectbox(
                "Choose the setup step:",
                [
                    "1. Initial Configuration",
                    "2. Configure AI APIs",
                    "3. Process First Test",
                    "4. Explore Analytics",
                    "5. Use Education Center"
                ]
            )

            if setup_step == "1. Initial Configuration":
                st.markdown("""
                ##### Welcome to LabsForm Pro!

                **What you can do:**
                - **Format lab results** into standardized text
                - **AI analysis** using Claude Sonnet 4 or GPT-4
                - **Advanced analytics** with charts and correlations
                - **Medical education** with our learning platform

                **Quick Start in 3 Steps:**
                """)

                col1, col2, col3 = st.columns(3)

                with col1:
                    st.markdown("""
                    **Step 1: Input your lab results**
                    ```
                    Hemoglobin: 14.2 g/dL
                    Glucose: 92 mg/dL
                    Creatinine: 0.9 mg/dL
                    ```
                    """)

                with col2:
                    st.markdown("""
                    **Step 2: Click Process**
                    • Automatic processing
                    • AI analyzes results
                    • Generates visualizations
                    """)

                with col3:
                    st.markdown("""
                    **Step 3: Use the result**
                    ```
                    Labs (03/15/2024):
                    Hemogram: /Hb 14.2g/dL/
                    Biochemistry: /Glu 92mg/dL/ /Cr 0.9mg/dL/
                    ```
                    """)

                # Quick demo
                st.markdown("##### Interactive Demo")

                if st.button("Test with Sample Data", type="primary"):
                    st.success("Sample data loaded successfully!")
                    st.code("""
Labs (03/15/2024): Hemogram: /Hb 14.2g/dL/ /Ht 42%/ /WBC 7500/mm³/ /Plt 280000/mm³/ Biochemistry: /Glu 92mg/dL/ /Cr 0.9mg/dL/ /Ur 28mg/dL/ /TC 185mg/dL/
                    """)
                    st.info("This is the ultra-compact format generated by LabsForm Pro!")

            elif setup_step == "2. Configure AI APIs":
                st.markdown("""
                ##### AI API Configuration (Optional)

                **Purpose:**
                - Intelligent analysis of results
                - Personalized recommendations
                - Advanced clinical insights
                - Contextual interpretation

                **Available Options:**
                - **Claude (Anthropic)**: Specialized in medical analysis
                - **GPT-4 (OpenAI)**: General analysis with good performance
                """)

                # API selection tabs
                api_config_tab1, api_config_tab2 = st.tabs(["Claude (Anthropic)", "GPT-4 (OpenAI)"])

                with api_config_tab1:
                    st.markdown("""
                    **How to configure Claude:**

                    **1. Get API Key:**
                    - Visit [console.anthropic.com](https://console.anthropic.com)
                    - Create account or login
                    - Go to "API Keys"
                    - Click "Create Key"
                    - Copy the generated key (starts with "sk-ant-")

                    **2. Configure in LabsForm:**
                    - Paste API key in "Anthropic API Key" field
                    - Enable "AI Analysis" in options
                    - Process a test to verify
                    """)

                    # Claude API key tester
                    test_claude_key = st.text_input(
                        "Test Claude API Key:",
                        type="password",
                        placeholder="sk-ant-api03-...",
                        key="test_claude"
                    )

                    if st.button("Test Claude", key="test_claude_btn"):
                        if test_claude_key:
                            if test_claude_key.startswith("sk-ant-"):
                                st.success("Valid Claude API key format!")
                            else:
                                st.error("Invalid format. Anthropic API keys start with 'sk-ant-'")
                        else:
                            st.warning("Enter an API key to test.")

                with api_config_tab2:
                    st.markdown("""
                    **How to configure OpenAI:**

                    **1. Get API Key:**
                    - Visit [platform.openai.com](https://platform.openai.com)
                    - Create account or login
                    - Go to "API Keys"
                    - Click "Create new secret key"
                    - Copy the generated key (starts with "sk-")

                    **2. Configure in LabsForm:**
                    - Paste API key in "OpenAI API Key" field
                    - Enable "AI Analysis" in options
                    - Process a test to verify

                    **Costs:**
                    - GPT-4: ~$0.03 per analysis
                    - Recommended: Set usage limits
                    """)

                    # OpenAI API key tester
                    test_openai_key = st.text_input(
                        "Test OpenAI API Key:",
                        type="password",
                        placeholder="sk-...",
                        key="test_openai"
                    )

                    if st.button("Test OpenAI", key="test_openai_btn"):
                        if test_openai_key:
                            if test_openai_key.startswith("sk-") and not test_openai_key.startswith("sk-ant-"):
                                st.success("Valid OpenAI API key format!")
                            else:
                                st.error("Invalid format. OpenAI API keys start with 'sk-' (but not 'sk-ant-')")
                        else:
                            st.warning("Enter an API key to test.")

                # Comparison table
                st.markdown("##### API Comparison")

                comparison_data = [
                    {"Feature": "Medical Specialization", "Claude": "Excellent", "GPT-4": "Very Good"},
                    {"Feature": "Speed", "Claude": "Good", "GPT-4": "Excellent"},
                    {"Feature": "Cost", "Claude": "Good", "GPT-4": "Moderate"},
                    {"Feature": "Language Support", "Claude": "Excellent", "GPT-4": "Excellent"},
                    {"Feature": "Availability", "Claude": "Good", "GPT-4": "Excellent"}
                ]

                df_comparison = pd.DataFrame(comparison_data)
                st.dataframe(df_comparison, use_container_width=True)

            elif setup_step == "3️⃣ Processar Primeiro Exame":
                st.markdown("##### 🧪 Tutorial: Seu Primeiro Exame")

                # Interactive tutorial
                tutorial_step = st.radio(
                    "Siga o tutorial passo a passo:",
                    [
                        "📝 Preparar dados",
                        "⚙️ Configurar opções",
                        "🚀 Processar",
                        "📊 Analisar resultados"
                    ],
                    horizontal=True
                )

                if tutorial_step == "📝 Preparar dados":
                    st.markdown("""
                    **📋 Formatos Aceitos:**

                    **✅ Texto simples:**
                    ```
                    Hemoglobina: 14.2 g/dL
                    Glicose: 92 mg/dL
                    Creatinina: 0.9 mg/dL
                    ```

                    **✅ Com data:**
                    ```
                    Data: 15/03/2024
                    Hemoglobina: 14.2 g/dL
                    Glicose: 92 mg/dL
                    ```

                    **✅ Formato de laboratório:**
                    ```
                    HEMOGRAMA COMPLETO
                    Hemoglobina............14.2 g/dL
                    Hematócrito............42%

                    BIOQUÍMICA SÉRICA
                    Glicose................92 mg/dL
                    ```
                    """)

                elif tutorial_step == "⚙️ Configurar opções":
                    st.markdown("""
                    **🎛️ Opções Recomendadas para Iniciantes:**

                    **Barra Lateral:**
                    - ✅ Histórico: Ativado
                    - ✅ Análise IA: Ativado (se tiver API key)
                    - ✅ Tendências: Ativado
                    - ✅ Correlação: Ativado

                    **Estilo de Formatação:**
                    - 🎯 **Compact**: Para uso clínico rápido
                    - 📋 **Detailed**: Para estudo e análise

                    **Incluir Referências:**
                    - ✅ Ativado para aprendizado
                    - ❌ Desativado para uso clínico
                    """)

                elif tutorial_step == "🚀 Processar":
                    st.markdown("""
                    **⚡ Processamento:**

                    1. **Cole ou upload** seus dados
                    2. **Configure** as opções desejadas
                    3. **Clique** "🚀 Processar Exames Ultra"
                    4. **Aguarde** o processamento (5-15 segundos)

                    **🔄 Durante o processamento:**
                    - 📊 Extração de dados
                    - 🤖 Análise com IA (se configurada)
                    - 📈 Geração de gráficos
                    - 🎯 Cálculo de scores
                    """)

                elif tutorial_step == "📊 Analisar resultados":
                    st.markdown("""
                    **📋 Interpretando os Resultados:**

                    **🎯 Resultado Formatado:**
                    - Texto padronizado para prontuário
                    - Formato ultra compacto com /abreviações/
                    - Data automática extraída

                    **🚨 Alertas:**
                    - 🔴 Valores altos
                    - 🔵 Valores baixos
                    - ✅ Valores normais

                    **🤖 Análise IA:**
                    - Resumo clínico
                    - Recomendações
                    - Insights inteligentes

                    **📊 Score de Saúde:**
                    - 0-100% baseado em normalidade
                    - Grades A+ até D
                    - Indicadores visuais
                    """)

        with help_tab2:
            st.markdown("#### 📖 Manual Completo do Usuário")

            # Manual sections
            manual_section = st.selectbox(
                "Escolha uma seção do manual:",
                [
                    "📋 Visão Geral do Sistema",
                    "🔧 Funcionalidades Detalhadas",
                    "🎛️ Configurações Avançadas",
                    "📊 Interpretação de Resultados",
                    "🤖 Recursos de IA",
                    "📈 Analytics e Visualizações",
                    "🎓 Sistema de Preceptoria",
                    "💾 Exportação e Backup"
                ]
            )

            if manual_section == "📋 Visão Geral do Sistema":
                st.markdown("""
                ##### 🎯 LabsForm Pro Ultra - Visão Geral Completa

                **🚀 Missão:**
                Transformar resultados de exames laboratoriais em texto padronizado,
                com análise inteligente e suporte educacional avançado.

                **🏗️ Arquitetura do Sistema:**

                ```
                ┌─────────────────────────────────────────────────────────┐
                │                 LABSFORM PRO ULTRA                      │
                ├─────────────────┬─────────────────┬─────────────────────┤
                │   PROCESSAMENTO │    ANALYTICS    │     PRECEPTORIA     │
                │                 │                 │                     │
                │ • Formatação    │ • Tendências    │ • Biblioteca        │
                │ • IA Claude     │ • Correlações   │ • Casos Clínicos    │
                │ • OCR/PDF       │ • Distribuições │ • Simuladores       │
                │ • Validação     │ • Insights      │ • Certificação      │
                └─────────────────┴─────────────────┴─────────────────────┘
                ```

                **🎯 Fluxo de Trabalho Principal:**

                1. **📥 ENTRADA**: Texto, PDF ou Imagem
                2. **🔍 PROCESSAMENTO**: Extração e validação
                3. **🤖 ANÁLISE**: IA Claude + Algoritmos
                4. **📊 VISUALIZAÇÃO**: Gráficos e métricas
                5. **📋 SAÍDA**: Texto formatado + Relatórios

                **🎨 Interface do Usuário:**
                - **Barra Lateral**: Configurações e opções
                - **Aba Processamento**: Entrada e resultados
                - **Aba Analytics**: Análises avançadas
                - **Aba Preceptoria**: Educação médica
                - **Aba Ajuda**: Suporte completo
                """)

            elif manual_section == "🔧 Funcionalidades Detalhadas":
                st.markdown("""
                ##### ⚙️ Funcionalidades Técnicas Detalhadas

                **📝 PROCESSAMENTO DE TEXTO:**
                - **Parser Inteligente**: Reconhece 50+ tipos de exames
                - **Extração de Data**: Automática com múltiplos formatos
                - **Normalização**: Unidades e valores padronizados
                - **Validação**: Verificação de consistência

                **🤖 INTELIGÊNCIA ARTIFICIAL:**
                - **Modelo**: Claude Sonnet 4 (Anthropic)
                - **Capacidades**:
                  - Análise contextual de resultados
                  - Geração de recomendações
                  - Identificação de padrões
                  - Correlações clínicas

                **📊 ANALYTICS AVANÇADOS:**
                - **Tendências Temporais**: Plotly interativo
                - **Correlações**: Matriz de Pearson
                - **Distribuições**: Análise estatística
                - **Score de Saúde**: Algoritmo proprietário

                **🎓 SISTEMA EDUCACIONAL:**
                - **Base de Conhecimento**: 1000+ conceitos
                - **Casos Clínicos**: Biblioteca interativa
                - **Simuladores**: Valores aleatórios/personalizados
                - **Avaliações**: Sistema de quiz e certificação
                """)

                # Technical specifications
                st.markdown("##### 🔧 Especificações Técnicas")

                tech_col1, tech_col2 = st.columns(2)

                with tech_col1:
                    st.markdown("""
                    **📋 Formatos Suportados:**
                    - Texto simples (.txt)
                    - PDF (.pdf)
                    - Imagens (.png, .jpg, .jpeg)
                    - Dados estruturados (JSON)

                    **🧪 Exames Reconhecidos:**
                    - Hemograma completo
                    - Bioquímica sérica
                    - Função hepática
                    - Perfil lipídico
                    - Função renal
                    - Hormônios tireoidianos
                    - Marcadores cardíacos
                    - Eletrólitos
                    - Coagulação
                    - Imunologia básica
                    """)

                with tech_col2:
                    st.markdown("""
                    **⚡ Performance:**
                    - Processamento: <5 segundos
                    - IA Analysis: 10-30 segundos
                    - Gráficos: <2 segundos
                    - Exportação: <1 segundo

                    **🔒 Segurança:**
                    - Dados não armazenados
                    - API keys criptografadas
                    - Sessão local apenas
                    - LGPD compliant

                    **🌐 Compatibilidade:**
                    - Navegadores modernos
                    - Desktop e mobile
                    - Offline (funcionalidades básicas)
                    """)

            elif manual_section == "📊 Interpretação de Resultados":
                st.markdown("""
                ##### 📊 Guia Completo de Interpretação

                **🎯 Formato de Saída Padrão:**
                ```
                Labs (DD/MM/AAAA): Categoria: /Exame Valor Unidade/ /Exame Valor Unidade/
                ```

                **📋 Exemplo Prático:**
                ```
                Labs (15/03/2024): Hemograma: /Hb 14.2g/dL/ /Ht 42%/ /Leuco 7500/mm³/
                Bioquímica: /Gli 92mg/dL/ /Cr 0.9mg/dL/ /Ur 28mg/dL/
                ```

                **🔍 Decodificação das Abreviações:**
                """)

                # Abbreviations table
                abbrev_data = [
                    {"Abreviação": "Hb", "Exame Completo": "Hemoglobina", "Categoria": "Hemograma"},
                    {"Abreviação": "Ht", "Exame Completo": "Hematócrito", "Categoria": "Hemograma"},
                    {"Abreviação": "Leuco", "Exame Completo": "Leucócitos", "Categoria": "Hemograma"},
                    {"Abreviação": "Plaq", "Exame Completo": "Plaquetas", "Categoria": "Hemograma"},
                    {"Abreviação": "Gli", "Exame Completo": "Glicose", "Categoria": "Bioquímica"},
                    {"Abreviação": "Cr", "Exame Completo": "Creatinina", "Categoria": "Bioquímica"},
                    {"Abreviação": "Ur", "Exame Completo": "Ureia", "Categoria": "Bioquímica"},
                    {"Abreviação": "CT", "Exame Completo": "Colesterol Total", "Categoria": "Lipídico"},
                    {"Abreviação": "TGP", "Exame Completo": "ALT/TGP", "Categoria": "Hepático"},
                    {"Abreviação": "TSH", "Exame Completo": "Hormônio Tireoestimulante", "Categoria": "Tireoide"}
                ]

                df_abbrev = pd.DataFrame(abbrev_data)
                st.dataframe(df_abbrev, use_container_width=True)

                st.markdown("""
                **🚨 Indicadores de Status:**
                - **Normal**: Sem indicadores especiais
                - **Alto**: ↑ ou 🔴 (dependendo da configuração)
                - **Baixo**: ↓ ou 🔵 (dependendo da configuração)
                - **Crítico**: ⚠️ ou 🚨 (valores perigosos)

                **📈 Score de Saúde:**
                - **90-100%**: A+ Excelente (🟢)
                - **80-89%**: A Muito Bom (🟡)
                - **70-79%**: B Bom (🟠)
                - **60-69%**: C Regular (🔴)
                - **<60%**: D Atenção (🚨)
                """)

        with help_tab3:
            st.markdown("#### 🔧 Troubleshooting - Solução de Problemas")

            # Problem categories
            problem_category = st.selectbox(
                "Selecione o tipo de problema:",
                [
                    "🚫 Erros de Processamento",
                    "🤖 Problemas com IA",
                    "📊 Gráficos não Aparecem",
                    "📱 Problemas de Interface",
                    "⚡ Performance Lenta",
                    "💾 Problemas de Download"
                ]
            )

            if problem_category == "🚫 Erros de Processamento":
                st.markdown("""
                ##### 🚫 Soluções para Erros de Processamento

                **❌ Problema: "Nenhum resultado encontrado"**

                **🔍 Possíveis Causas:**
                - Formato de texto não reconhecido
                - Valores sem unidades
                - Nomes de exames em idioma não suportado

                **✅ Soluções:**
                1. **Verificar formato**: Use o exemplo fornecido
                2. **Incluir unidades**: Ex: "14.2 g/dL" não "14.2"
                3. **Nomes padrão**: Use "Hemoglobina" não "Hgb"
                4. **Separar linhas**: Um exame por linha

                **📋 Formato Recomendado:**
                ```
                Data: 15/03/2024

                Hemoglobina: 14.2 g/dL
                Hematócrito: 42%
                Glicose: 92 mg/dL
                Creatinina: 0.9 mg/dL
                ```
                """)

                # Interactive troubleshooter
                st.markdown("##### 🔧 Diagnóstico Interativo")

                user_text = st.text_area(
                    "Cole seu texto problemático aqui:",
                    placeholder="Cole o texto que não está sendo processado corretamente..."
                )

                if st.button("🔍 Diagnosticar Problema"):
                    if user_text:
                        issues = []

                        # Check for common issues
                        if not any(word in user_text.lower() for word in ['hemoglobina', 'glicose', 'creatinina']):
                            issues.append("⚠️ Nenhum exame reconhecido encontrado")

                        if not any(unit in user_text for unit in ['g/dL', 'mg/dL', '%', '/mm³']):
                            issues.append("⚠️ Unidades não encontradas")

                        if ':' not in user_text and '=' not in user_text:
                            issues.append("⚠️ Separadores de valor não encontrados")

                        if len(user_text.split('\n')) < 2:
                            issues.append("⚠️ Texto em uma linha só")

                        if issues:
                            st.error("🚨 Problemas encontrados:")
                            for issue in issues:
                                st.write(f"• {issue}")
                        else:
                            st.success("✅ Formato parece correto!")
                    else:
                        st.warning("⚠️ Cole algum texto para diagnosticar.")

            elif problem_category == "🤖 Problemas com IA":
                st.markdown("""
                ##### 🤖 Soluções para Problemas com IA

                **❌ Problema: "Análise de IA não disponível"**

                **🔍 Causa:** API key não configurada ou inválida

                **✅ Soluções:**
                1. **Verificar API key**: Deve começar com "sk-ant-"
                2. **Testar conexão**: Use o testador na aba Início Rápido
                3. **Verificar créditos**: Acesse console.anthropic.com
                4. **Recarregar página**: Às vezes resolve problemas temporários

                **❌ Problema: "Erro na análise de IA"**

                **🔍 Possíveis Causas:**
                - Limite de rate exceeded
                - Texto muito longo
                - Problemas de conectividade

                **✅ Soluções:**
                1. **Aguardar**: Rate limits se resetam em 1 minuto
                2. **Texto menor**: Máximo 1000 caracteres
                3. **Verificar internet**: Conexão estável necessária
                4. **Tentar novamente**: Problemas temporários são comuns
                """)

                # API status checker
                st.markdown("##### 🔍 Verificador de Status da API")

                api_status_col1, api_status_col2 = st.columns(2)

                with api_status_col1:
                    if st.button("🔍 Verificar Status Anthropic"):
                        st.info("🌐 Verificando status da API Anthropic...")
                        # Simulate API check
                        st.success("✅ API Anthropic operacional")

                with api_status_col2:
                    if st.button("🧪 Testar Conexão"):
                        st.info("🔗 Testando conectividade...")
                        # Simulate connection test
                        st.success("✅ Conexão com internet OK")

        with help_tab4:
            st.markdown("#### 💡 FAQ - Perguntas Frequentes")

            # FAQ categories
            faq_category = st.selectbox(
                "Escolha uma categoria de perguntas:",
                [
                    "🎯 Uso Geral",
                    "🤖 Inteligência Artificial",
                    "📊 Analytics e Gráficos",
                    "🎓 Preceptoria e Educação",
                    "🔒 Privacidade e Segurança",
                    "💰 Custos e Limites"
                ]
            )

            if faq_category == "🎯 Uso Geral":
                st.markdown("##### 🎯 Perguntas sobre Uso Geral")

                # Expandable FAQ items
                with st.expander("❓ O que é o LabsForm Pro Ultra?"):
                    st.markdown("""
                    O LabsForm Pro Ultra é uma ferramenta avançada para formatação e análise de exames laboratoriais.
                    Ele converte resultados de exames em texto padronizado, oferece análise com IA e recursos educacionais.

                    **Principais funcionalidades:**
                    - 📝 Formatação automática de exames
                    - 🤖 Análise inteligente com Claude AI
                    - 📊 Analytics avançados com gráficos
                    - 🎓 Sistema de preceptoria médica
                    """)

                with st.expander("❓ Quais tipos de exames são suportados?"):
                    st.markdown("""
                    **✅ Exames Suportados:**
                    - 🩸 Hemograma completo
                    - 🧪 Bioquímica sérica (glicose, creatinina, ureia, etc.)
                    - 💊 Função hepática (TGO, TGP, bilirrubinas)
                    - ❤️ Perfil lipídico (colesterol, triglicerídeos)
                    - 🦴 Função renal (creatinina, ureia, clearance)
                    - 🔬 Hormônios tireoidianos (TSH, T3, T4)
                    - ⚡ Eletrólitos (sódio, potássio, cálcio)
                    - 🫀 Marcadores cardíacos (troponina, CK-MB)
                    - 🩺 Coagulação (TP, TTPA, INR)
                    - 🛡️ Imunologia básica (PCR, VHS)

                    **📈 Em desenvolvimento:**
                    - Urinálise completa
                    - Hormônios reprodutivos
                    - Marcadores tumorais
                    - Gasometria arterial
                    """)

                with st.expander("❓ Como interpretar o formato de saída?"):
                    st.markdown("""
                    **📋 Formato Padrão:**
                    ```
                    Labs (Data): Categoria: /Abrev Valor Unidade/ /Abrev Valor Unidade/
                    ```

                    **🔍 Exemplo:**
                    ```
                    Labs (15/03/2024): Hemograma: /Hb 14.2g/dL/ /Ht 42%/ Bioquímica: /Gli 92mg/dL/
                    ```

                    **📖 Significado:**
                    - **Labs**: Identificador padrão
                    - **(Data)**: Data do exame extraída automaticamente
                    - **Categoria**: Tipo de exame (Hemograma, Bioquímica, etc.)
                    - **/Abrev/**: Abreviação do exame entre barras
                    - **Valor**: Resultado numérico
                    - **Unidade**: Unidade de medida
                    """)

                with st.expander("❓ Posso usar sem API key da Anthropic?"):
                    st.markdown("""
                    **✅ Sim! Funcionalidades sem API key:**
                    - 📝 Formatação básica de exames
                    - 📊 Gráficos e visualizações
                    - 🎯 Score de saúde
                    - 📋 Tabelas de resultados
                    - 🎓 Conteúdo educacional
                    - 💾 Download de relatórios

                    **🤖 Funcionalidades que requerem API key:**
                    - Análise inteligente com IA
                    - Recomendações personalizadas
                    - Insights clínicos avançados
                    - Interpretação contextual

                    **💡 Recomendação:** Use sem API key para aprender e testar,
                    configure a API key quando quiser análises avançadas.
                    """)

            elif faq_category == "🤖 Inteligência Artificial":
                st.markdown("##### 🤖 Perguntas sobre IA")

                with st.expander("❓ Qual modelo de IA é usado?"):
                    st.markdown("""
                    **🧠 Modelo:** Claude Sonnet 4 da Anthropic

                    **🎯 Por que Claude?**
                    - 📚 Conhecimento médico atualizado
                    - 🎯 Precisão em análises clínicas
                    - 🔒 Foco em segurança e ética
                    - 🌐 Suporte a português brasileiro

                    **⚡ Capacidades:**
                    - Análise contextual de resultados
                    - Identificação de padrões anômalos
                    - Geração de recomendações
                    - Correlações entre exames
                    - Interpretação clínica avançada
                    """)

                with st.expander("❓ A IA pode substituir um médico?"):
                    st.markdown("""
                    **❌ NÃO! A IA é uma ferramenta auxiliar.**

                    **🎯 O que a IA faz:**
                    - ✅ Identifica valores alterados
                    - ✅ Sugere possíveis correlações
                    - ✅ Oferece recomendações gerais
                    - ✅ Destaca padrões importantes

                    **⚠️ O que a IA NÃO faz:**
                    - ❌ Diagnósticos definitivos
                    - ❌ Prescrições médicas
                    - ❌ Substituição de consulta médica
                    - ❌ Decisões clínicas finais

                    **👨‍⚕️ SEMPRE consulte um médico para:**
                    - Interpretação definitiva
                    - Diagnósticos
                    - Tratamentos
                    - Decisões clínicas
                    """)

                with st.expander("❓ Como garantir a precisão da IA?"):
                    st.markdown("""
                    **🔍 Medidas de Qualidade:**

                    **1. Validação Cruzada:**
                    - ✅ Compare com valores de referência
                    - ✅ Verifique consistência entre exames
                    - ✅ Analise tendências temporais

                    **2. Limitações Conhecidas:**
                    - ⚠️ IA pode ter vieses
                    - ⚠️ Não considera histórico completo
                    - ⚠️ Baseada em padrões gerais

                    **3. Boas Práticas:**
                    - 📋 Use como segunda opinião
                    - 👨‍⚕️ Sempre consulte profissional
                    - 📚 Continue estudando medicina
                    - 🔄 Atualize conhecimentos regularmente
                    """)

            elif faq_category == "🔒 Privacidade e Segurança":
                st.markdown("##### 🔒 Perguntas sobre Privacidade")

                with st.expander("❓ Meus dados são armazenados?"):
                    st.markdown("""
                    **🛡️ NÃO! Seus dados são seguros.**

                    **🔒 Política de Privacidade:**
                    - ❌ Nenhum dado é armazenado permanentemente
                    - ❌ Não criamos banco de dados
                    - ❌ Não compartilhamos informações
                    - ✅ Processamento apenas na sessão atual

                    **💾 O que acontece com os dados:**
                    1. **Durante uso**: Dados ficam na memória local
                    2. **IA Analysis**: Enviados temporariamente para Anthropic
                    3. **Fim da sessão**: Todos os dados são apagados
                    4. **Recarregar página**: Tudo é perdido

                    **🌐 Conformidade:**
                    - ✅ LGPD compliant
                    - ✅ GDPR compliant
                    - ✅ HIPAA aware
                    """)

                with st.expander("❓ A API key é segura?"):
                    st.markdown("""
                    **🔐 Sim, sua API key é protegida.**

                    **🛡️ Medidas de Segurança:**
                    - 🔒 Armazenamento local apenas
                    - 🔒 Não enviada para nossos servidores
                    - 🔒 Criptografia durante transmissão
                    - 🔒 Apagada ao fechar navegador

                    **⚠️ Responsabilidades do usuário:**
                    - 🔑 Não compartilhe sua API key
                    - 🔄 Monitore uso na Anthropic
                    - 🚫 Não use em computadores públicos
                    - 🔒 Mantenha navegador atualizado

                    **💡 Dica:** Use API keys com limites baixos para testes.
                    """)

        with help_tab5:
            st.markdown("#### 🎥 Tutoriais em Vídeo")

            st.info("🚧 **Seção em Desenvolvimento**")

            st.markdown("""
            ##### 📺 Tutoriais Planejados

            **🎬 Série 1: Básico**
            - 🎯 Introdução ao LabsForm Pro Ultra (5 min)
            - 📝 Primeiro processamento de exames (8 min)
            - ⚙️ Configurações essenciais (6 min)
            - 📊 Interpretando resultados (10 min)

            **🎬 Série 2: Avançado**
            - 🤖 Configurando IA Claude (7 min)
            - 📈 Analytics e gráficos (12 min)
            - 🔍 Troubleshooting comum (9 min)
            - 💾 Exportação e relatórios (5 min)

            **🎬 Série 3: Preceptoria**
            - 🎓 Usando a biblioteca médica (15 min)
            - 🧠 Resolvendo casos clínicos (20 min)
            - 🎯 Simuladores e quizzes (10 min)
            - 🏆 Sistema de certificação (8 min)

            **📅 Cronograma:**
            - ✅ Roteiros finalizados
            - 🎬 Gravação: Janeiro 2024
            - 🎞️ Edição: Fevereiro 2024
            - 🚀 Lançamento: Março 2024
            """)

            # Placeholder for video tutorials
            tutorial_type = st.selectbox(
                "Escolha o tipo de tutorial:",
                ["🎯 Demonstração Interativa", "📋 Tutorial Textual", "🎮 Tutorial Gamificado"]
            )

            if tutorial_type == "🎯 Demonstração Interativa":
                st.markdown("##### 🎯 Demo: Processamento Completo")

                demo_step = st.radio(
                    "Siga a demonstração:",
                    ["1️⃣ Preparar", "2️⃣ Configurar", "3️⃣ Processar", "4️⃣ Analisar"],
                    horizontal=True
                )

                if demo_step == "1️⃣ Preparar":
                    st.markdown("""
                    **📋 Dados de Exemplo:**
                    ```
                    Data: 15/03/2024

                    HEMOGRAMA COMPLETO
                    Hemoglobina: 14.2 g/dL
                    Hematócrito: 42%
                    Leucócitos: 7.500/mm³
                    Plaquetas: 280.000/mm³

                    BIOQUÍMICA SÉRICA
                    Glicose: 92 mg/dL
                    Creatinina: 0.9 mg/dL
                    Ureia: 28 mg/dL
                    ```

                    **✅ Próximo passo:** Vá para "Configurar"
                    """)

                elif demo_step == "2️⃣ Configurar":
                    st.markdown("""
                    **⚙️ Configurações Recomendadas:**

                    **Barra Lateral:**
                    - 📊 Histórico: ✅ Ativado
                    - 🤖 Análise IA: ✅ Ativado
                    - 📈 Tendências: ✅ Ativado
                    - 🔥 Correlação: ✅ Ativado

                    **Estilo:** Compact
                    **Referências:** ❌ Desativado

                    **✅ Próximo passo:** Vá para "Processar"
                    """)

                elif demo_step == "3️⃣ Processar":
                    st.markdown("""
                    **🚀 Processamento:**

                    1. Cole os dados na área de texto
                    2. Clique "🚀 Processar Exames Ultra"
                    3. Aguarde 5-15 segundos
                    4. Veja os resultados aparecerem

                    **🔄 Durante o processamento:**
                    - Extração automática de dados
                    - Validação de valores
                    - Análise com IA (se configurada)
                    - Geração de gráficos

                    **✅ Próximo passo:** Vá para "Analisar"
                    """)

                elif demo_step == "4️⃣ Analisar":
                    st.markdown("""
                    **📊 Resultado Esperado:**
                    ```
                    Labs (15/03/2024): Hemograma: /Hb 14.2g/dL/ /Ht 42%/ /Leuco 7500/mm³/ /Plaq 280000/mm³/ Bioquímica: /Gli 92mg/dL/ /Cr 0.9mg/dL/ /Ur 28mg/dL/
                    ```

                    **🎯 Score de Saúde:** ~95% (A+ Excelente)

                    **✅ Valores Normais:** Todos os exames

                    **🤖 Análise IA:** "Resultados dentro da normalidade..."

                    **🎉 Parabéns!** Você completou seu primeiro processamento!
                    """)

        with help_tab6:
            st.markdown("#### 📞 Suporte e Contato")

            # Support options
            support_col1, support_col2 = st.columns(2)

            with support_col1:
                st.markdown("""
                ##### 🆘 Canais de Suporte

                **📧 Email Técnico:**
                - 🔧 Problemas técnicos: `<EMAIL>`
                - 🐛 Bugs e erros: `<EMAIL>`
                - 💡 Sugestões: `<EMAIL>`

                **💬 Chat Online:**
                - 🕐 Segunda a Sexta: 9h às 18h
                - ⚡ Resposta em até 2 horas
                - 🌐 Disponível em português

                **📱 WhatsApp Business:**
                - 📞 +55 (11) 99999-9999
                - 🕐 Segunda a Sexta: 9h às 17h
                - 📋 Apenas suporte técnico
                """)

            with support_col2:
                st.markdown("""
                ##### 🌐 Recursos Online

                **📚 Documentação:**
                - 📖 Manual completo online
                - 🎥 Tutoriais em vídeo
                - 📋 FAQ atualizado

                **👥 Comunidade:**
                - 💬 Fórum de usuários
                - 📱 Grupo no Telegram
                - 🐦 Twitter @LabsFormPro

                **🔄 Atualizações:**
                - 📧 Newsletter mensal
                - 🚀 Release notes
                - 🐛 Bug fixes semanais
                """)

            # Contact form
            st.markdown("##### 📝 Formulário de Contato")

            with st.form("contact_form"):
                contact_name = st.text_input("Nome completo:")
                contact_email = st.text_input("Email:")
                contact_type = st.selectbox(
                    "Tipo de solicitação:",
                    ["🐛 Bug Report", "💡 Sugestão", "❓ Dúvida Técnica", "🎓 Suporte Educacional", "💰 Comercial"]
                )
                contact_priority = st.selectbox(
                    "Prioridade:",
                    ["🔴 Alta (Sistema não funciona)", "🟡 Média (Funcionalidade com problema)", "🟢 Baixa (Melhoria/Sugestão)"]
                )
                contact_message = st.text_area(
                    "Descreva sua solicitação:",
                    placeholder="Descreva detalhadamente o problema ou sugestão..."
                )

                submitted = st.form_submit_button("📤 Enviar Solicitação")

                if submitted:
                    if contact_name and contact_email and contact_message:
                        st.success("✅ Solicitação enviada com sucesso!")
                        st.info("📧 Você receberá uma resposta em até 24 horas.")

                        # Show ticket details
                        import random
                        ticket_number = f"LF{random.randint(100000, 999999)}"
                        st.code(f"Número do ticket: {ticket_number}")

                        # Show summary
                        st.markdown("**📋 Resumo da solicitação:**")
                        st.write(f"• **Tipo**: {contact_type}")
                        st.write(f"• **Prioridade**: {contact_priority}")
                        st.write(f"• **Contato**: {contact_email}")
                    else:
                        st.error("❌ Preencha todos os campos obrigatórios.")

            # System status
            st.markdown("##### 🔍 Status do Sistema")

            status_col1, status_col2, status_col3 = st.columns(3)

            with status_col1:
                st.metric("🖥️ Sistema Principal", "✅ Online", "99.9% uptime")

            with status_col2:
                st.metric("🤖 API Claude", "✅ Operacional", "< 2s resposta")

            with status_col3:
                st.metric("📊 Analytics", "✅ Funcionando", "< 1s gráficos")

            # Version info
            st.markdown("##### ℹ️ Informações da Versão")

            version_info = {
                "Versão": "3.7.0 Ultra",
                "Build": "20240315-1200",
                "Última Atualização": "15/03/2024",
                "Próxima Versão": "3.8.0 (Abril 2024)"
            }

            for key, value in version_info.items():
                st.write(f"**{key}:** {value}")

    with tab5:
        # Clinical Notes Management Interface
        st.markdown("""
        <div class="feature-card">
            <h2 style="color: #dc2626; font-weight: 700; margin-bottom: 0.5rem;">Clinical Notes Management Center</h2>
            <p style="color: #6b7280; margin: 0;">Comprehensive note management and clinical documentation system</p>
        </div>
        """, unsafe_allow_html=True)

        # Initialize notes if not exists
        if 'clinical_notes' not in st.session_state:
            st.session_state.clinical_notes = []

        if st.session_state.clinical_notes:
            # Notes statistics
            col_stats1, col_stats2, col_stats3, col_stats4 = st.columns(4)

            with col_stats1:
                st.metric("Total Notes", len(st.session_state.clinical_notes))

            with col_stats2:
                # Count notes from today
                from datetime import datetime
                today = datetime.now().strftime('%d/%m/%Y')
                today_notes = len([n for n in st.session_state.clinical_notes if n['date_created'] == today])
                st.metric("Today's Notes", today_notes)

            with col_stats3:
                # Average note length
                avg_length = sum(len(n['content']) for n in st.session_state.clinical_notes) // len(st.session_state.clinical_notes)
                st.metric("Avg. Length", f"{avg_length} chars")

            with col_stats4:
                # Most recent note
                latest_note = max(st.session_state.clinical_notes, key=lambda x: x['timestamp'])
                st.metric("Latest Note", latest_note['date_created'])

            st.divider()

            # Search and filter interface
            col_search, col_sort, col_actions = st.columns([2, 1, 1])

            with col_search:
                search_term = st.text_input(
                    "Search Notes",
                    placeholder="Search by title or content...",
                    key="notes_search_main"
                )

            with col_sort:
                sort_order = st.selectbox(
                    "Sort by",
                    ["Newest First", "Oldest First", "Title A-Z", "Title Z-A"],
                    key="notes_sort_main"
                )

            with col_actions:
                if st.button("Export All Notes", use_container_width=True):
                    # Create export content
                    export_content = "# Clinical Notes Export\n\n"
                    export_content += f"Export Date: {datetime.now().strftime('%d/%m/%Y %H:%M')}\n"
                    export_content += f"Total Notes: {len(st.session_state.clinical_notes)}\n\n"
                    export_content += "=" * 50 + "\n\n"

                    for note in st.session_state.clinical_notes:
                        export_content += f"## {note['title']}\n"
                        export_content += f"**Created:** {note['timestamp']}\n"
                        export_content += f"**ID:** {note['id']}\n\n"
                        export_content += f"{note['content']}\n\n"
                        export_content += "-" * 30 + "\n\n"

                    st.download_button(
                        label="Download Notes File",
                        data=export_content,
                        file_name=f"clinical_notes_export_{datetime.now().strftime('%Y%m%d_%H%M')}.txt",
                        mime="text/plain",
                        use_container_width=True
                    )

            # Filter and sort notes
            notes = st.session_state.clinical_notes.copy()

            # Apply search filter
            if search_term:
                notes = [
                    note for note in notes
                    if search_term.lower() in note['title'].lower()
                    or search_term.lower() in note['content'].lower()
                ]

            # Apply sorting
            if sort_order == "Newest First":
                notes = sorted(notes, key=lambda x: x['timestamp'], reverse=True)
            elif sort_order == "Oldest First":
                notes = sorted(notes, key=lambda x: x['timestamp'])
            elif sort_order == "Title A-Z":
                notes = sorted(notes, key=lambda x: x['title'].lower())
            elif sort_order == "Title Z-A":
                notes = sorted(notes, key=lambda x: x['title'].lower(), reverse=True)

            # Show search results info
            if search_term:
                st.info(f"Found {len(notes)} note(s) matching '{search_term}'")

            st.divider()

            # Display notes in a grid layout
            if notes:
                # Pagination
                notes_per_page = 6
                total_pages = (len(notes) - 1) // notes_per_page + 1

                if total_pages > 1:
                    col_prev, col_page, col_next = st.columns([1, 2, 1])

                    with col_prev:
                        if st.button("Previous", disabled=st.session_state.get('notes_page', 1) <= 1):
                            st.session_state.notes_page = max(1, st.session_state.get('notes_page', 1) - 1)
                            st.rerun()

                    with col_page:
                        current_page = st.session_state.get('notes_page', 1)
                        st.markdown(f"<div style='text-align: center; padding: 0.5rem;'>Page {current_page} of {total_pages}</div>", unsafe_allow_html=True)

                    with col_next:
                        if st.button("Next", disabled=st.session_state.get('notes_page', 1) >= total_pages):
                            st.session_state.notes_page = min(total_pages, st.session_state.get('notes_page', 1) + 1)
                            st.rerun()

                # Calculate page slice
                current_page = st.session_state.get('notes_page', 1)
                start_idx = (current_page - 1) * notes_per_page
                end_idx = start_idx + notes_per_page
                page_notes = notes[start_idx:end_idx]

                # Display notes in rows of 2
                for i in range(0, len(page_notes), 2):
                    col1, col2 = st.columns(2)

                    # First note
                    with col1:
                        if i < len(page_notes):
                            note = page_notes[i]
                            with st.container():
                                st.markdown(f"""
                                <div class="clinical-note-card">
                                    <div class="note-header">
                                        <h4 class="note-title">{note['title']}</h4>
                                        <small class="note-date">{note['date_created']}</small>
                                    </div>
                                    <p class="note-content-preview">{note['content'][:150]}{'...' if len(note['content']) > 150 else ''}</p>
                                </div>
                                """, unsafe_allow_html=True)

                                # Note actions
                                col_view, col_edit, col_del = st.columns(3)
                                with col_view:
                                    if st.button("View", key=f"view_main_{note['id']}", use_container_width=True):
                                        st.session_state[f"show_note_main_{note['id']}"] = True
                                        st.rerun()

                                with col_edit:
                                    if st.button("Edit", key=f"edit_main_{note['id']}", use_container_width=True):
                                        st.session_state[f"edit_note_main_{note['id']}"] = True
                                        st.rerun()

                                with col_del:
                                    if st.button("Delete", key=f"del_main_{note['id']}", use_container_width=True):
                                        if st.session_state.get(f"confirm_del_{note['id']}", False):
                                            st.session_state.clinical_notes = [n for n in st.session_state.clinical_notes if n['id'] != note['id']]
                                            st.session_state[f"confirm_del_{note['id']}"] = False
                                            st.success("Note deleted successfully")
                                            st.rerun()
                                        else:
                                            st.session_state[f"confirm_del_{note['id']}"] = True
                                            st.warning("Click again to confirm")
                                            st.rerun()

                    # Second note
                    with col2:
                        if i + 1 < len(page_notes):
                            note = page_notes[i + 1]
                            with st.container():
                                st.markdown(f"""
                                <div class="clinical-note-card">
                                    <div class="note-header">
                                        <h4 class="note-title">{note['title']}</h4>
                                        <small class="note-date">{note['date_created']}</small>
                                    </div>
                                    <p class="note-content-preview">{note['content'][:150]}{'...' if len(note['content']) > 150 else ''}</p>
                                </div>
                                """, unsafe_allow_html=True)

                                # Note actions
                                col_view, col_edit, col_del = st.columns(3)
                                with col_view:
                                    if st.button("View", key=f"view_main_{note['id']}", use_container_width=True):
                                        st.session_state[f"show_note_main_{note['id']}"] = True
                                        st.rerun()

                                with col_edit:
                                    if st.button("Edit", key=f"edit_main_{note['id']}", use_container_width=True):
                                        st.session_state[f"edit_note_main_{note['id']}"] = True
                                        st.rerun()

                                with col_del:
                                    if st.button("Delete", key=f"del_main_{note['id']}", use_container_width=True):
                                        if st.session_state.get(f"confirm_del_{note['id']}", False):
                                            st.session_state.clinical_notes = [n for n in st.session_state.clinical_notes if n['id'] != note['id']]
                                            st.session_state[f"confirm_del_{note['id']}"] = False
                                            st.success("Note deleted successfully")
                                            st.rerun()
                                        else:
                                            st.session_state[f"confirm_del_{note['id']}"] = True
                                            st.warning("Click again to confirm")
                                            st.rerun()

                # Handle note viewing and editing modals
                for note in page_notes:
                    # View modal
                    if st.session_state.get(f"show_note_main_{note['id']}", False):
                        with st.expander(f"📋 {note['title']}", expanded=True):
                            st.markdown(f"""
                            <div class="note-full-content">
                                <h5 class="note-full-title">{note['title']}</h5>
                                <p class="note-full-text">{note['content']}</p>
                                <hr style="margin: 1rem 0; border: none; height: 1px; background: #e2e8f0;">
                                <small style="color: #6b7280;">Created: {note['timestamp']} | ID: {note['id']}</small>
                            </div>
                            """, unsafe_allow_html=True)

                            if st.button("Close", key=f"close_main_{note['id']}"):
                                st.session_state[f"show_note_main_{note['id']}"] = False
                                st.rerun()

                    # Edit modal
                    if st.session_state.get(f"edit_note_main_{note['id']}", False):
                        with st.expander(f"✏️ Edit: {note['title']}", expanded=True):
                            with st.form(f"edit_form_main_{note['id']}"):
                                edited_title = st.text_input("Title", value=note['title'])
                                edited_content = st.text_area("Content", value=note['content'], height=200)

                                col_save, col_cancel = st.columns(2)
                                with col_save:
                                    if st.form_submit_button("Save Changes", type="primary", use_container_width=True):
                                        # Update the note
                                        for n in st.session_state.clinical_notes:
                                            if n['id'] == note['id']:
                                                n['title'] = edited_title
                                                n['content'] = edited_content
                                                n['timestamp'] = datetime.now().strftime('%Y-%m-%d %H:%M')
                                                break
                                        st.session_state[f"edit_note_main_{note['id']}"] = False
                                        st.success("Note updated successfully")
                                        st.rerun()

                                with col_cancel:
                                    if st.form_submit_button("Cancel", use_container_width=True):
                                        st.session_state[f"edit_note_main_{note['id']}"] = False
                                        st.rerun()

            else:
                if search_term:
                    st.warning(f"No notes found matching '{search_term}'")
                else:
                    st.info("No notes to display")

            # Bulk actions
            st.divider()
            st.markdown("### Bulk Actions")

            col_bulk1, col_bulk2, col_bulk3 = st.columns(3)

            with col_bulk1:
                if st.button("Clear All Notes", use_container_width=True):
                    if st.session_state.get('confirm_clear_all', False):
                        st.session_state.clinical_notes = []
                        st.session_state.confirm_clear_all = False
                        st.success("All notes cleared successfully")
                        st.rerun()
                    else:
                        st.session_state.confirm_clear_all = True
                        st.warning("Click again to confirm deletion of ALL notes")
                        st.rerun()

            with col_bulk2:
                if st.button("Generate Report", use_container_width=True):
                    # Generate statistics report
                    report = f"""# Clinical Notes Report
Generated: {datetime.now().strftime('%d/%m/%Y %H:%M')}

## Statistics
- Total Notes: {len(st.session_state.clinical_notes)}
- Average Length: {sum(len(n['content']) for n in st.session_state.clinical_notes) // len(st.session_state.clinical_notes)} characters
- Date Range: {min(n['date_created'] for n in st.session_state.clinical_notes)} to {max(n['date_created'] for n in st.session_state.clinical_notes)}

## Notes by Date
"""
                    # Group by date
                    from collections import defaultdict
                    by_date = defaultdict(int)
                    for note in st.session_state.clinical_notes:
                        by_date[note['date_created']] += 1

                    for date, count in sorted(by_date.items()):
                        report += f"- {date}: {count} note(s)\n"

                    st.download_button(
                        label="Download Report",
                        data=report,
                        file_name=f"notes_report_{datetime.now().strftime('%Y%m%d_%H%M')}.txt",
                        mime="text/plain",
                        use_container_width=True
                    )

            with col_bulk3:
                if st.button("Refresh View", use_container_width=True):
                    st.rerun()

        else:
            # Empty state
            st.markdown("""
            <div style="text-align: center; padding: 4rem 2rem; color: #6b7280;">
                <h3>No Clinical Notes Yet</h3>
                <p>Start creating clinical notes using the sidebar form, or process laboratory results to auto-generate notes.</p>
                <p>Your notes will appear here for easy management and access.</p>
            </div>
            """, unsafe_allow_html=True)

    # Ultra Modern Enterprise Footer
    st.markdown("""
    <div style="
        background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
        color: white;
        text-align: center;
        padding: 3rem 2rem;
        border-radius: 16px;
        margin-top: 4rem;
        box-shadow: 0 20px 40px rgba(220, 38, 38, 0.2);
        position: relative;
        overflow: hidden;
    ">
        <div style="position: relative; z-index: 1;">
            <h3 style="font-weight: 800; margin-bottom: 1rem; font-size: 1.5rem;">LabsForm Pro Enterprise</h3>
            <p style="opacity: 0.9; margin-bottom: 1rem; font-size: 1.1rem;">Advanced Medical Laboratory Data Processing Platform</p>
            <p style="opacity: 0.8; font-size: 0.95rem;">Always consult healthcare professionals for clinical interpretation</p>
            <div style="margin-top: 2rem; padding-top: 2rem; border-top: 1px solid rgba(255,255,255,0.2);">
                <p style="opacity: 0.7; font-size: 0.9rem;">Enterprise Support: <EMAIL> | Version 3.7.0 Enterprise Edition</p>
            </div>
        </div>
        <div style="
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: pulse 6s ease-in-out infinite;
        "></div>
    </div>
    """, unsafe_allow_html=True)

if __name__ == "__main__":
    main()
