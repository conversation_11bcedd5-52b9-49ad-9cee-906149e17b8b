#!/usr/bin/env python3
"""
Teste das melhorias na formatação de texto puro do ai_labs_formatter.py
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ai_labs_formatter import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, LabTextParser

def test_enhanced_text_parsing():
    """Teste da análise de texto puro melhorada"""
    
    # Criar instância do parser
    parser = LabTextParser()
    formatter = AILabFormatter()
    
    # Texto de exemplo com diferentes formatos
    test_texts = [
        # Formato 1: Padrão com dois pontos
        """
        Hemoglobina: 14.2 g/dL
        Hematócrito: 42.5%
        Leucócitos: 7500 /mm³
        Glicose: 95 mg/dL
        Creatinina: 1.1 mg/dL
        """,
        
        # Formato 2: Formato mais livre
        """
        Hb 13.8 g/dL
        Ht 41%
        Glicose jejum 88 mg/dL
        Ureia 35 mg/dL
        Colesterol total 180 mg/dL
        HDL 55 mg/dL
        LDL 110 mg/dL
        """,
        
        # Formato 3: Formato com parênteses
        """
        Hemoglobina (14.5 g/dL)
        Plaquetas (250000 /mm³)
        TSH (2.1 mUI/L)
        T4 livre (1.2 ng/dL)
        """,
        
        # Formato 4: Formato científico
        """
        Eritrócitos: 4.5 x 10^6 /mm³
        Leucócitos: 8.2 x 10³ /mm³
        Vitamina B12: 450 pg/mL
        Ferritina: 85 ng/mL
        """,
        
        # Formato 5: Formato misto com diferentes separadores
        """
        ALT = 28 U/L
        AST - 32 U/L
        Bilirrubina total: 0.8 mg/dL
        Albumina 4.2 g/dL
        PCR 1.5 mg/L
        """
    ]
    
    print("=== TESTE DAS MELHORIAS NA FORMATAÇÃO DE TEXTO PURO ===\n")
    
    for i, text in enumerate(test_texts, 1):
        print(f"--- TESTE {i} ---")
        print(f"Texto original:")
        print(text.strip())
        print()
        
        # Parse do texto
        results = parser.parse_text(text)
        
        print(f"Resultados encontrados: {len(results)}")
        for result in results:
            print(f"  • {result.test_name}: {result.value} {result.unit} ({result.status}) [{result.category}]")
        print()
        
        # Formatação final
        if results:
            formatted = formatter.format_results(results, "25/06/2025")
            print(f"Formatação final:")
            print(formatted)
        else:
            print("Nenhum resultado formatado.")
        
        print("\n" + "="*80 + "\n")

def test_abbreviation_mapping():
    """Teste do mapeamento de abreviações"""
    
    formatter = AILabFormatter()
    
    test_names = [
        "Hemoglobina",
        "Hematócrito", 
        "Glicose",
        "Creatinina",
        "Colesterol Total",
        "HDL Colesterol",
        "TSH",
        "T4 Livre",
        "ALT",
        "AST",
        "Proteína C Reativa"
    ]
    
    print("=== TESTE DO MAPEAMENTO DE ABREVIAÇÕES ===\n")
    
    for test_name in test_names:
        abbrev = formatter._get_enhanced_abbreviation(test_name)
        print(f"{test_name:20} -> {abbrev}")
    
    print("\n" + "="*50 + "\n")

if __name__ == "__main__":
    test_enhanced_text_parsing()
    test_abbreviation_mapping()
